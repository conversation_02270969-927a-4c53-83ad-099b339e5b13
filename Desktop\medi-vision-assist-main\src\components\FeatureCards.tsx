
import React from 'react';
import { Eye, Users, Shield, Zap, Languages, Heart } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const FeatureCards = () => {
  const features = [
    {
      icon: Eye,
      title: 'Accessibility First',
      description: 'Designed for visually impaired and elderly users with high contrast, large fonts, and screen reader support.'
    },
    {
      icon: Users,
      title: 'For Everyone',
      description: 'Perfect for general public, pharmacists, travelers, and healthcare professionals.'
    },
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'Your health data is encrypted and stored securely. We never share your information.'
    },
    {
      icon: Zap,
      title: 'Instant Results',
      description: 'Get medicine identification and detailed information in seconds, not minutes.'
    },
    {
      icon: Languages,
      title: 'Multilingual',
      description: 'Available in English, French, and German with culturally appropriate medical guidance.'
    },
    {
      icon: Heart,
      title: 'Healthcare Grade',
      description: 'Built with medical professionals using verified pharmaceutical databases.'
    }
  ];

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto max-w-6xl">
        <h2 className="text-3xl font-bold text-center mb-12 text-foreground">
          Why Choose MediScan?
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="border-2 hover:border-primary/20 transition-colors">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureCards;
