
const intentService = require('../services/intentService');
const responseService = require('../services/responseService');
const conversationService = require('../services/conversationService');

class ChatController {
  async processMessage(req, res) {
    try {
      const { message, sessionId = 'default' } = req.body;
      
      console.log(`Processing message: "${message}" for session: ${sessionId}`);
      
      // Detect intent from user message
      const intent = intentService.detectIntent(message);
      
      // Generate appropriate response with session context
      const response = await responseService.generateResponse(intent, message, sessionId);
      
      // Store conversation
      conversationService.addMessage(sessionId, {
        user: message,
        bot: response.text,
        intent: intent.name,
        confidence: intent.confidence,
        entities: intent.entities,
        timestamp: new Date()
      });

      res.json({
        success: true,
        data: {
          response: response.text,
          intent: intent.name,
          confidence: intent.confidence,
          suggestions: response.suggestions || [],
          sessionId
        }
      });
    } catch (error) {
      console.error('Error processing message:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process message',
        message: 'I apologize, but I encountered an error. Please try again.'
      });
    }
  }

  async getConversation(req, res) {
    try {
      const { sessionId } = req.params;
      const conversation = conversationService.getConversation(sessionId);
      
      res.json({
        success: true,
        data: {
          sessionId,
          messages: conversation,
          messageCount: conversation.length
        }
      });
    } catch (error) {
      console.error('Error retrieving conversation:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve conversation'
      });
    }
  }

  async clearConversation(req, res) {
    try {
      const { sessionId } = req.params;
      conversationService.clearConversation(sessionId);
      
      res.json({
        success: true,
        message: 'Conversation cleared successfully'
      });
    } catch (error) {
      console.error('Error clearing conversation:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to clear conversation'
      });
    }
  }
}

module.exports = new ChatController();
