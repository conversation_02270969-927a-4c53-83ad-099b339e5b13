
import React, { useState } from 'react';
import { Menu, X, Globe, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { language, setLanguage } = useLanguage();
  const { t } = useTranslation();

  const languages = {
    en: t('common.english'),
    fr: t('common.french'),
    de: t('common.german')
  };

  return (
    <nav className="bg-primary text-primary-foreground sticky top-0 z-50 border-b">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="text-2xl font-bold">
            MediScan
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/identify" className="hover:text-primary-foreground/80 transition-colors">
              {t('nav.identifyMedicine')}
            </Link>
            <Link to="/chat" className="hover:text-primary-foreground/80 transition-colors">
              {t('nav.aiAssistant')}
            </Link>
            <Link to="/dashboard" className="hover:text-primary-foreground/80 transition-colors">
              {t('nav.dashboard')}
            </Link>
            
            {/* Language Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-primary-foreground hover:text-primary-foreground/80">
                  <Globe className="h-4 w-4 mr-2" />
                  {languages[language]}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setLanguage('en')}>
                  {t('common.english')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage('fr')}>
                  {t('common.french')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage('de')}>
                  {t('common.german')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-primary-foreground hover:text-primary-foreground/80">
                  <User className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem asChild>
                  <Link to="/login">{t('nav.login')}</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/register">{t('nav.register')}</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-primary-foreground"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-primary-foreground/20">
            <div className="space-y-4">
              <Link 
                to="/identify" 
                className="block hover:text-primary-foreground/80 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('nav.identifyMedicine')}
              </Link>
              <Link 
                to="/chat" 
                className="block hover:text-primary-foreground/80 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('nav.aiAssistant')}
              </Link>
              <Link 
                to="/dashboard" 
                className="block hover:text-primary-foreground/80 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('nav.dashboard')}
              </Link>
              <div className="flex space-x-4 pt-2">
                <Link to="/login">
                  <Button variant="outline" size="sm">{t('nav.login')}</Button>
                </Link>
                <Link to="/register">
                  <Button variant="secondary" size="sm">{t('nav.register')}</Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
