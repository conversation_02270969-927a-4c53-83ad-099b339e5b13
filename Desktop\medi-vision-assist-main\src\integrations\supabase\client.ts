// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ygkxdctaraeragizxfbt.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlna3hkY3RhcmFlcmFnaXp4ZmJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNDU3MTksImV4cCI6MjA2NTgyMTcxOX0.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);