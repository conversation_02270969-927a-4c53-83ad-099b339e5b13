
import React from 'react';
import { History, Heart, Shield, Settings, Camera, Search, MessageCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import { useTranslation } from '@/hooks/useTranslation';

const Dashboard = () => {
  const { t } = useTranslation();
  
  // Mock user data
  const recentSearches = [
    { id: 1, medicine: "Acetaminophen 500mg", method: "Photo", date: "2024-01-15", confidence: 95 },
    { id: 2, medicine: "Ibuprofen 200mg", method: "Name Search", date: "2024-01-14", confidence: 100 },
    { id: 3, medicine: "Lisinopril 10mg", method: "Description", date: "2024-01-12", confidence: 87 },
  ];

  const userStats = {
    totalSearches: 24,
    successfulIdentifications: 22,
    favoriteMedicines: 5,
    accountCreated: "2023-12-01"
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('dashboard.title')}</h1>
          <p className="text-muted-foreground">{t('dashboard.subtitle')}</p>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Link to="/identify?method=photo">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="flex items-center space-x-4 p-6">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Camera className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{t('dashboard.identifyByPhoto')}</h3>
                  <p className="text-sm text-muted-foreground">{t('dashboard.uploadMedicineImage')}</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link to="/identify?method=search">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="flex items-center space-x-4 p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Search className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{t('dashboard.searchByName')}</h3>
                  <p className="text-sm text-muted-foreground">{t('dashboard.findMedicineInfo')}</p>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link to="/chat">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="flex items-center space-x-4 p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{t('dashboard.aiAssistant')}</h3>
                  <p className="text-sm text-muted-foreground">{t('dashboard.chatForGuidance')}</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <History className="h-5 w-5" />
                  <span>{t('dashboard.recentSearches')}</span>
                </CardTitle>
                <CardDescription>{t('dashboard.recentSearchesDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentSearches.map((search) => (
                    <div key={search.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{search.medicine}</h4>
                        <p className="text-sm text-muted-foreground">
                          {search.method} • {new Date(search.date).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge className={`${search.confidence >= 90 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {search.confidence}% {t('dashboard.match')}
                      </Badge>
                    </div>
                  ))}
                </div>
                <Button variant="outline" className="w-full mt-4">
                  {t('dashboard.viewAllHistory')}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* User Stats & Settings */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Heart className="h-5 w-5" />
                  <span>{t('dashboard.yourStats')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('dashboard.totalSearches')}</span>
                  <span className="font-medium">{userStats.totalSearches}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('dashboard.successRate')}</span>
                  <span className="font-medium">
                    {Math.round((userStats.successfulIdentifications / userStats.totalSearches) * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('dashboard.savedMedicines')}</span>
                  <span className="font-medium">{userStats.favoriteMedicines}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('dashboard.memberSince')}</span>
                  <span className="font-medium">
                    {new Date(userStats.accountCreated).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>{t('dashboard.privacySecurity')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">{t('dashboard.dataEncryption')}</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">{t('dashboard.enabled')}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">{t('dashboard.searchHistory')}</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">{t('dashboard.private')}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">{t('dashboard.twoFactorAuth')}</span>
                  <Badge variant="outline">{t('dashboard.setupRequired')}</Badge>
                </div>
                <Button variant="outline" size="sm" className="w-full mt-4">
                  <Settings className="h-4 w-4 mr-2" />
                  {t('dashboard.privacySettings')}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('dashboard.accessibilityOptions')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  {t('dashboard.enableLargeText')}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  {t('dashboard.highContrast')}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  {t('dashboard.screenReader')}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
