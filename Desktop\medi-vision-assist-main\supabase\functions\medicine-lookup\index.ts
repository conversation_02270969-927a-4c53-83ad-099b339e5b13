
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RxNormResponse {
  idGroup?: {
    rxnormId?: string[]
  }
}

interface MedicineProperties {
  propConceptGroup?: {
    propConcept?: Array<{
      propName: string
      propValue: string
    }>
  }
}

interface SpellingResponse {
  suggestionGroup?: {
    suggestionList?: {
      suggestion?: string[]
    }
  }
}

interface RelatedResponse {
  relatedGroup?: {
    conceptGroup?: Array<{
      tty?: string
      conceptProperties?: Array<{
        rxcui: string
        name: string
        synonym?: string
        tty?: string
        language?: string
        suppress?: string
        umlscui?: string
      }>
    }>
  }
}

async function getRxCUI(medicineName: string): Promise<string | null> {
  try {
    const response = await fetch(`https://rxnav.nlm.nih.gov/REST/rxcui.json?name=${encodeURIComponent(medicineName)}`);
    const data: RxNormResponse = await response.json();
    
    if (data.idGroup?.rxnormId && data.idGroup.rxnormId.length > 0) {
      return data.idGroup.rxnormId[0];
    }
    return null;
  } catch (error) {
    console.error('Error getting RxCUI:', error);
    return null;
  }
}

async function getMedicineDetails(rxcui: string) {
  try {
    const response = await fetch(`https://rxnav.nlm.nih.gov/REST/rxcui/${rxcui}/allProperties.json?prop=all`);
    const data: MedicineProperties = await response.json();
    
    const properties: Record<string, string> = {};
    
    if (data.propConceptGroup?.propConcept) {
      data.propConceptGroup.propConcept.forEach(prop => {
        properties[prop.propName] = prop.propValue;
      });
    }
    
    return properties;
  } catch (error) {
    console.error('Error getting medicine details:', error);
    return null;
  }
}

async function getSpellingSuggestions(input: string): Promise<string[]> {
  try {
    const response = await fetch(`https://rxnav.nlm.nih.gov/REST/spellingsuggestions.json?name=${encodeURIComponent(input)}`);
    const data: SpellingResponse = await response.json();
    
    return data.suggestionGroup?.suggestionList?.suggestion || [];
  } catch (error) {
    console.error('Error getting spelling suggestions:', error);
    return [];
  }
}

async function getRelatedMedicines(rxcui: string): Promise<Array<{name: string, type: string}>> {
  try {
    const response = await fetch(`https://rxnav.nlm.nih.gov/REST/rxcui/${rxcui}/related.json?tty=SCD+SBD+GPCK+BPCK`);
    const data: RelatedResponse = await response.json();
    
    const related: Array<{name: string, type: string}> = [];
    
    if (data.relatedGroup?.conceptGroup) {
      data.relatedGroup.conceptGroup.forEach((group) => {
        if (group.conceptProperties) {
          group.conceptProperties.forEach((concept) => {
            if (concept.name && related.length < 8) {
              // Clean up the medicine name to make it more readable
              const cleanName = cleanMedicineName(concept.name);
              const type = group.tty || 'Related';
              
              if (cleanName && !related.some(r => r.name === cleanName)) {
                related.push({
                  name: cleanName,
                  type: type
                });
              }
            }
          });
        }
      });
    }
    
    return related;
  } catch (error) {
    console.error('Error getting related medicines:', error);
    return [];
  }
}

function cleanMedicineName(name: string): string {
  // Extract brand names in brackets
  const brandMatch = name.match(/\[([^\]]+)\]/);
  if (brandMatch) {
    const brandName = brandMatch[1];
    
    // Extract dosage information before the brand name
    const beforeBrand = name.substring(0, name.indexOf('['));
    const dosageMatch = beforeBrand.match(/(\d+(?:\.\d+)?\s*(?:MG|mg|ML|ml))/gi);
    
    if (dosageMatch && dosageMatch.length > 0) {
      return `${brandName} (${dosageMatch.join(', ')})`;
    }
    
    return brandName;
  }
  
  // If no brand name, try to extract a cleaner version
  const parts = name.split(/\s+/);
  const cleanParts = parts.filter(part => 
    !part.match(/^\d+$/) && // Remove standalone numbers
    !part.match(/^(Oral|Tablet|Capsule|Extended|Release|mg|MG|ml|ML)$/i) // Remove common medical terms
  ).slice(0, 3); // Take first 3 meaningful words
  
  return cleanParts.join(' ');
}

function formatRelatedMedicines(related: Array<{name: string, type: string}>): string {
  if (related.length === 0) {
    return 'No related medicines found in database';
  }
  
  // Group by brand/type for better organization
  const formatted = related.map(med => med.name).join(', ');
  
  return formatted;
}

function getMedicineUsage(medicineName: string, properties: Record<string, string>): string {
  const name = medicineName.toLowerCase();
  
  // Common medicine usage patterns
  const usageMap: Record<string, string> = {
    'metformin': 'Used to treat type 2 diabetes by controlling blood sugar levels',
    'ibuprofen': 'Used to reduce fever, pain, and inflammation',
    'paracetamol': 'Used to treat pain and reduce fever',
    'acetaminophen': 'Used to treat pain and reduce fever',
    'aspirin': 'Used to reduce pain, fever, and inflammation; also used for heart protection',
    'amoxicillin': 'An antibiotic used to treat bacterial infections',
    'lisinopril': 'Used to treat high blood pressure and heart failure',
    'amlodipine': 'Used to treat high blood pressure and chest pain (angina)',
    'omeprazole': 'Used to reduce stomach acid and treat heartburn, GERD, and ulcers',
    'simvastatin': 'Used to lower cholesterol and reduce risk of heart disease'
  };
  
  return usageMap[name] || 'General therapeutic use - consult your healthcare provider for specific information';
}

function getDosageInfo(medicineName: string, properties: Record<string, string>): string {
  const name = medicineName.toLowerCase();
  
  // Common dosage information
  const dosageMap: Record<string, string> = {
    'metformin': 'Typically 500-1000mg twice daily with meals',
    'ibuprofen': 'Usually 200-400mg every 4-6 hours as needed',
    'paracetamol': 'Usually 500-1000mg every 4-6 hours, maximum 4000mg daily',
    'acetaminophen': 'Usually 500-1000mg every 4-6 hours, maximum 4000mg daily',
    'aspirin': 'Varies from 75mg daily for heart protection to 300-600mg for pain relief',
    'amoxicillin': 'Typically 250-500mg three times daily',
    'lisinopril': 'Usually started at 5-10mg once daily',
    'amlodipine': 'Usually 5-10mg once daily',
    'omeprazole': 'Usually 20mg once daily before meals',
    'simvastatin': 'Usually 10-40mg once daily in the evening'
  };
  
  return dosageMap[name] || 'Follow prescription instructions or consult your healthcare provider';
}

function formatMedicineInfo(medicineName: string, properties: Record<string, string>, related: Array<{name: string, type: string}>) {
  const productName = properties.RxNorm || medicineName;
  const type = properties.TTY?.includes('SCD') || properties.TTY?.includes('SBD') ? 'Medicine' : 'Supplement';
  
  const usage = getMedicineUsage(medicineName, properties);
  const dosage = getDosageInfo(medicineName, properties);
  const similarMeds = formatRelatedMedicines(related);
  
  return {
    product: productName,
    type: type,
    usage: usage,
    dosage: dosage,
    similar: similarMeds,
    confidence: 95,
    formatted: `🔹 Product: ${productName}
🔸 Type: ${type}
💊 Use: ${usage}
📏 General Dosage: ${dosage}
🔁 Similar Medicines: ${similarMeds}

⚠️ Important: This information is for educational purposes only. Always consult a healthcare professional for personalized medical advice and proper dosage instructions.`
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { medicineName } = await req.json();
    
    if (!medicineName || typeof medicineName !== 'string') {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Medicine name is required' 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    console.log(`Looking up medicine: ${medicineName}`);
    
    // Step 1: Get RxCUI
    let rxcui = await getRxCUI(medicineName);
    
    if (!rxcui) {
      // Try spelling suggestions
      const suggestions = await getSpellingSuggestions(medicineName);
      
      if (suggestions.length > 0) {
        console.log(`Trying spelling suggestion: ${suggestions[0]}`);
        rxcui = await getRxCUI(suggestions[0]);
      }
      
      if (!rxcui) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "I couldn't find official data for this product. Please double-check the spelling or consult a pharmacist.",
            suggestions: suggestions
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404,
          }
        );
      }
    }

    console.log(`Found RxCUI: ${rxcui}`);
    
    // Step 2: Get medicine details
    const properties = await getMedicineDetails(rxcui);
    
    if (!properties) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "I couldn't retrieve detailed information for this medicine. Please consult a pharmacist."
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      );
    }

    // Step 3: Get related medicines
    const related = await getRelatedMedicines(rxcui);
    
    // Format the response
    const medicineInfo = formatMedicineInfo(medicineName, properties, related);
    
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          medicine: medicineInfo,
          rxcui: rxcui,
          properties: properties
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Error in medicine-lookup function:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error',
        message: 'An error occurred while looking up the medicine. Please try again.'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
