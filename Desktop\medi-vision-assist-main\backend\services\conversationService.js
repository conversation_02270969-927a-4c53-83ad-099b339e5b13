class ConversationService {
  constructor() {
    this.conversations = new Map();
  }

  addMessage(sessionId, messageData) {
    if (!this.conversations.has(sessionId)) {
      this.conversations.set(sessionId, []);
    }
    
    const conversation = this.conversations.get(sessionId);
    conversation.push({
      ...messageData,
      id: this.generateMessageId(),
      timestamp: messageData.timestamp || new Date()
    });

    // Keep only last 50 messages per session to manage memory
    if (conversation.length > 50) {
      conversation.splice(0, conversation.length - 50);
    }
  }

  getConversation(sessionId) {
    return this.conversations.get(sessionId) || [];
  }

  clearConversation(sessionId) {
    this.conversations.delete(sessionId);
  }

  getAllSessions() {
    return Array.from(this.conversations.keys());
  }

  getSessionStats(sessionId) {
    const conversation = this.getConversation(sessionId);
    const intentCounts = {};
    
    conversation.forEach(message => {
      if (message.intent) {
        intentCounts[message.intent] = (intentCounts[message.intent] || 0) + 1;
      }
    });

    return {
      messageCount: conversation.length,
      intentDistribution: intentCounts,
      lastActivity: conversation.length > 0 ? conversation[conversation.length - 1].timestamp : null
    };
  }

  generateMessageId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

module.exports = new ConversationService();
