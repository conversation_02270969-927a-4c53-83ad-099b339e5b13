{"Commands:": "Buyruqlar:", "Options:": "Imkoniyatlar:", "Examples:": "<PERSON><PERSON><PERSON>:", "boolean": "boolean", "count": "sanoq", "string": "satr", "number": "raqam", "array": "massiv", "required": "ma<PERSON><PERSON><PERSON><PERSON>", "default": "boshlang'ich", "default:": "boshlang'ich:", "choices:": "tanlovlar:", "aliases:": "taxalluslar:", "generated-value": "<PERSON><PERSON><PERSON><PERSON>-qi<PERSON><PERSON>", "Not enough non-option arguments: got %s, need at least %s": {"one": "No-imkoniyat argumentlar yetarli emas: berilgan %s, minimum %s", "other": "No-imkoniyat argumentlar yetarli emas: berilgan %s, minimum %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "No-imkoniyat argumentlar juda ko'p: berilgan %s, maksim<PERSON> %s", "other": "No-imkoniyat argumentlar juda ko'p: got %s, maksimum %s"}, "Missing argument value: %s": {"one": "Argument qiymati berilmagan: %s", "other": "Argument qiymatlari berilmagan: %s"}, "Missing required argument: %s": {"one": "Majburiy argument berilmagan: %s", "other": "Majburiy argumentlar berilmagan: %s"}, "Unknown argument: %s": {"one": "Noma'lum argument berilmagan: %s", "other": "Noma'lum argumentlar berilmagan: %s"}, "Invalid values:": "<PERSON><PERSON>z qi<PERSON>:", "Argument: %s, Given: %s, Choices: %s": "Argument: %s, Berilgan: %s, Tanlovlar: %s", "Argument check failed: %s": "Muvaffaqiyatsiz argument tekshiruvi: %s", "Implications failed:": "<PERSON>g'liq <PERSON><PERSON> be<PERSON>:", "Not enough arguments following: %s": "Quyidagi argumentlar yetarli emas: %s", "Invalid JSON config file: %s": "Nosoz JSON konfiguratsiya fayli: %s", "Path to JSON config file": "JSON konfiguratsiya fayli <PERSON>", "Show help": "<PERSON><PERSON><PERSON> ko'r<PERSON>ish", "Show version number": "<PERSON><PERSON><PERSON><PERSON> ko'rsatish", "Did you mean %s?": "%s ni nazarda tutyapsizmi?", "Arguments %s and %s are mutually exclusive": "%s va %s <PERSON><PERSON>i alohida", "Positionals:": "Positsionallar:", "command": "buyruq", "deprecated": "<PERSON><PERSON><PERSON>", "deprecated: %s": "eskirgan: %s"}