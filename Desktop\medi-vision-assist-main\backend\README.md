
# Medical Chatbot Backend

A professional Node.js backend for an AI-powered medical assistant chatbot that helps users identify medicines and provides health guidance.

## Features

- **Intent Recognition**: Smart pattern matching for user queries
- **Modular Architecture**: Clean separation of concerns
- **Conversation Management**: Session-based chat history
- **Medical Focus**: Specialized responses for health-related queries
- **Extensible**: Ready for AI integration (OpenAI, Dialogflow)
- **Professional**: Production-ready with error handling and validation

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the server:**
   ```bash
   # Development
   npm run dev

   # Production
   npm start
   ```

4. **Test the API:**
   ```bash
   curl http://localhost:3001/health
   ```

## API Endpoints

### Chat
- `POST /api/chat/message` - Send a message to the chatbot
- `GET /api/chat/conversation/:sessionId` - Get conversation history
- `DELETE /api/chat/conversation/:sessionId` - Clear conversation

### Example Request
```json
POST /api/chat/message
{
  "message": "Hi, what is this app?",
  "sessionId": "user123"
}
```

### Example Response
```json
{
  "success": true,
  "data": {
    "response": "Our app helps you identify medicines...",
    "intent": "app_info",
    "confidence": 0.95,
    "suggestions": ["Upload a photo", "Search by name"],
    "sessionId": "user123"
  }
}
```

## Supported Intents

- **greet**: Welcome messages
- **ask_bot_status**: Bot status queries
- **app_info**: App purpose and features
- **symptom_query**: Health symptom discussions
- **medicine_identification**: Medicine identification requests
- **dosage_question**: Dosage and usage queries
- **side_effects**: Side effect information
- **emergency**: Emergency situations

## Project Structure

```
backend/
├── server.js              # Main application entry
├── routes/
│   └── chatRoutes.js      # Chat API routes
├── controllers/
│   └── chatController.js  # Request handling logic
├── services/
│   ├── intentService.js   # Intent detection
│   ├── responseService.js # Response generation
│   └── conversationService.js # Chat history
├── config/
│   └── intents.js         # Intent definitions
├── middleware/
│   ├── validation.js      # Input validation
│   └── errorHandler.js    # Error handling
└── package.json
```

## Future AI Integration

The backend is structured to easily integrate with:
- OpenAI GPT models
- Google Dialogflow
- Custom ML models
- External medical APIs

Uncomment and configure the relevant environment variables in `.env` when ready.

## Development

Run with auto-reload:
```bash
npm run dev
```

The server will restart automatically when you make changes to the code.
