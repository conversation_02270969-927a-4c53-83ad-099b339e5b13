
class ResponseService {
  constructor() {
    this.responseHistory = new Map(); // Track previous responses per session
  }

  async generateResponse(intent, originalMessage, sessionId = 'default') {
    const responses = this.getResponseTemplates();
    const intentName = intent.name;
    
    if (responses[intentName]) {
      const templateResponse = responses[intentName];
      
      // Get a non-repetitive response
      const responseText = this.selectNonRepetitiveResponse(
        templateResponse.messages, 
        sessionId, 
        intentName
      );
      
      // Enhance response with entity information
      const enhancedText = this.enhanceResponseWithEntities(responseText, intent.entities);
      
      return {
        text: enhancedText,
        suggestions: templateResponse.suggestions || [],
        requiresFollowUp: templateResponse.requiresFollowUp || false
      };
    }

    // Enhanced fallback response based on message content
    return this.generateFallbackResponse(originalMessage);
  }

  selectNonRepetitiveResponse(messages, sessionId, intentName) {
    const sessionKey = `${sessionId}_${intentName}`;
    
    // Get previous responses for this session and intent
    const previousResponses = this.responseHistory.get(sessionKey) || [];
    
    // Filter out recently used responses
    const availableMessages = messages.filter(msg => 
      !previousResponses.includes(msg) || previousResponses.length >= messages.length
    );
    
    // Select a random response from available options
    const selectedMessage = availableMessages.length > 0 
      ? availableMessages[Math.floor(Math.random() * availableMessages.length)]
      : messages[Math.floor(Math.random() * messages.length)];
    
    // Update response history
    if (previousResponses.length >= messages.length) {
      // Reset history if we've used all responses
      this.responseHistory.set(sessionKey, [selectedMessage]);
    } else {
      this.responseHistory.set(sessionKey, [...previousResponses, selectedMessage]);
    }
    
    return selectedMessage;
  }

  enhanceResponseWithEntities(responseText, entities) {
    if (!entities || Object.keys(entities).length === 0) {
      return responseText;
    }

    let enhancedText = responseText;

    // Add specific mentions of detected symptoms
    if (entities.symptoms && entities.symptoms.length > 0) {
      enhancedText += ` I noticed you mentioned ${entities.symptoms.join(', ')}. Let me help you with that.`;
    }

    // Add specific mentions of detected medicines
    if (entities.medicines && entities.medicines.length > 0) {
      enhancedText += ` I see you're asking about ${entities.medicines.join(', ')}.`;
    }

    // Add severity context
    if (entities.severity && entities.severity !== 'unknown') {
      if (entities.severity === 'severe') {
        enhancedText += ` Since you mentioned this is severe, please consider consulting a healthcare professional.`;
      }
    }

    return enhancedText;
  }

  generateFallbackResponse(originalMessage) {
    const message = originalMessage.toLowerCase();
    
    // Smart fallback based on message content
    if (message.includes('photo') || message.includes('picture') || message.includes('image')) {
      return {
        text: "I'd be happy to help you identify a medicine from a photo! You can upload an image and I'll help you identify what it is.",
        suggestions: ["Upload photo for identification", "Describe the medicine instead", "Search by name"]
      };
    }
    
    if (message.includes('name') || message.includes('search')) {
      return {
        text: "You can search for medicines by typing their name. I'll help you find information about dosage, side effects, and usage.",
        suggestions: ["Search by medicine name", "Upload a photo instead", "Tell me your symptoms"]
      };
    }
    
    if (message.includes('help') || message.includes('how')) {
      return {
        text: "I'm here to help! I can identify medicines from photos, provide information about medications, help with symptoms, and guide you through our app features.",
        suggestions: ["Upload medicine photo", "Search by name", "Ask about symptoms", "Learn about features"]
      };
    }
    
    // Default enhanced fallback
    return {
      text: "I want to help you, but I'm not sure I understood correctly. Could you please rephrase your question? I'm great at identifying medicines, providing health guidance, and explaining our app features.",
      suggestions: [
        "Upload a photo to identify medicine",
        "Search by medicine name",
        "Describe your symptoms",
        "Tell me about this app"
      ]
    };
  }

  getResponseTemplates() {
    return {
      greet: {
        messages: [
          "Hello! I'm your AI medical assistant. I can help you identify medicines, understand symptoms, and provide health guidance. How can I assist you today?",
          "Hi there! Welcome to our medical guidance app. I'm here to help you with medicine identification and health questions. What would you like to know?",
          "Hello! I'm ready to help you with any medicine-related questions or health concerns. How may I assist you?",
          "Hi! I'm your personal medical AI assistant. Whether you need to identify a medicine or have health questions, I'm here to help!"
        ],
        suggestions: [
          "Identify a medicine by photo",
          "Search for a medicine by name",
          "Ask about symptoms",
          "Learn about this app"
        ]
      },

      ask_bot_status: {
        messages: [
          "I'm doing great and ready to help! My systems are running smoothly and I'm here to assist with all your medical questions.",
          "I'm working perfectly and standing by to help you with medicine identification and health guidance!",
          "All systems operational! I'm ready to help you identify medicines, understand symptoms, and provide health information.",
          "I'm functioning excellently and excited to help! Whether it's medicine identification or health guidance, I'm at your service."
        ],
        suggestions: [
          "Upload medicine photo",
          "Ask about symptoms", 
          "Search medicine database",
          "Learn about app features"
        ]
      },

      app_info: {
        messages: [
          "Our app is designed to help you quickly identify any medicine just by uploading a photo, typing its name, or entering a description. Whether you have a blurry label, only part of the name, or just basic info — I'll help you find the correct medicine and provide detailed information including usage instructions, side effects, and alternatives.",
          "This is a comprehensive medicine identification and guidance app. I can help you identify medications through photos, names, or descriptions, and provide detailed information about dosage, side effects, usage instructions, and safe alternatives. It's especially useful when you're unsure about a medication.",
          "Welcome to your medicine identification assistant! I help eliminate confusion with medications by providing instant identification through photos, names, or descriptions, plus detailed guidance on usage, dosage, side effects, and safety information.",
          "I'm part of an intelligent medicine identification system that helps you understand any medication. Simply upload a photo, search by name, or describe what you have, and I'll provide comprehensive information to keep you safe and informed."
        ],
        suggestions: [
          "Upload a photo to identify medicine",
          "Search by medicine name", 
          "Describe medicine appearance",
          "Ask about safety and side effects"
        ]
      },

      symptom_query: {
        messages: [
          "I understand you're experiencing symptoms. While I can provide general information, please remember that I cannot diagnose or replace professional medical advice. Can you tell me more about your symptoms so I can suggest appropriate over-the-counter options or recommend when to see a healthcare provider?",
          "Thank you for sharing your symptoms with me. I can help suggest general remedies and when to seek medical attention, but please consult a healthcare professional for proper diagnosis. Could you describe your symptoms in more detail?",
          "I'm here to help with your health concerns. While I can provide general guidance and suggest common remedies, please remember that serious or persistent symptoms require professional medical attention. Tell me more about what you're experiencing.",
          "I want to help you feel better. I can suggest over-the-counter options and general advice based on your symptoms, but remember that I'm not a replacement for professional medical care. What specific symptoms are you dealing with?"
        ],
        suggestions: [
          "Describe symptoms in detail",
          "Ask about over-the-counter remedies",
          "Find medicine for pain relief", 
          "When should I see a doctor?"
        ],
        requiresFollowUp: true
      },

      medicine_identification: {
        messages: [
          "I'd be happy to help identify your medicine! For the most accurate results, please upload a clear photo of the medication. Make sure any text, numbers, or markings are visible. You can also describe the pill's color, shape, and any imprints.",
          "Let's identify that medicine together! The best way is to upload a photo showing any text, numbers, or markings on the pill. If you can't take a photo, describe the medicine's appearance - color, shape, size, and any letters or numbers you see.",
          "I'm ready to help identify your medication. Please upload a clear photo if possible, or provide a detailed description including color, shape, size, and any text or markings visible on the medicine.",
          "Perfect! I love helping with medicine identification. Upload a photo for the most accurate results, or tell me about the pill's appearance - every detail helps me identify it correctly."
        ],
        suggestions: [
          "Upload photo of medicine",
          "Describe pill appearance", 
          "Enter medicine name if known",
          "Check for identifying markings"
        ]
      },

      dosage_question: {
        messages: [
          "Dosage information is crucial for safe medication use. I can provide general guidance, but always follow the instructions on your medication label or consult your healthcare provider. Can you tell me which specific medicine you're asking about?",
          "Proper dosage is essential for medication safety and effectiveness. While I can share general information, please always follow your prescription label or package instructions. Which medication would you like dosage information for?",
          "I can help with general dosage guidelines, but remember that dosing can vary based on individual factors. Always consult the medication label, package insert, or your healthcare provider. What specific medicine are you asking about?",
          "Dosage questions are important for your safety. I can provide general guidelines, but your specific dosage should always come from your healthcare provider or the medication packaging. Which medicine do you need dosage information for?"
        ],
        suggestions: [
          "Upload photo of medicine label",
          "Search specific medicine",
          "Ask about missed doses", 
          "Consult healthcare provider"
        ]
      },

      side_effects: {
        messages: [
          "I can provide information about common side effects, but everyone reacts differently to medications. If you're experiencing concerning symptoms, please contact your healthcare provider. Which medication would you like side effect information for?",
          "Side effects can vary from person to person. I can share general information about common reactions, but if you're experiencing unusual symptoms, please consult a healthcare professional. What medicine are you concerned about?",
          "Understanding potential side effects is important for safe medication use. I can provide general information, but please seek immediate medical attention for severe reactions. Which medication interests you?",
          "I'm here to help you understand side effects. While I can share common reactions and safety information, always contact your doctor if you experience concerning symptoms. What medication are you asking about?"
        ],
        suggestions: [
          "Search specific medicine",
          "Report severe side effects",
          "Contact healthcare provider",
          "Learn about drug interactions"
        ]
      },

      emergency: {
        messages: [
          "⚠️ If you're experiencing a medical emergency, please call emergency services immediately (911 in the US) or go to your nearest emergency room. I'm not a substitute for emergency medical care.",
          "🚨 This sounds like it could be urgent. Please seek immediate medical attention by calling emergency services or visiting an emergency room. Don't delay getting professional help.",
          "⚠️ For any medical emergency or severe reaction, please contact emergency services right away. I cannot provide emergency medical care - please get professional help immediately."
        ],
        suggestions: [
          "Call Emergency Services (911)",
          "Go to Emergency Room", 
          "Contact Your Doctor",
          "Call Poison Control if applicable"
        ]
      }
    };
  }
}

module.exports = new ResponseService();
