
import React from 'react';
import { Shield } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslation } from '@/hooks/useTranslation';

interface TermsSectionProps {
  acceptTerms: boolean;
  acceptPrivacy: boolean;
  setAcceptTerms: (checked: boolean) => void;
  setAcceptPrivacy: (checked: boolean) => void;
}

const TermsSection = ({ 
  acceptTerms, 
  acceptPrivacy, 
  setAcceptTerms, 
  setAcceptPrivacy 
}: TermsSectionProps) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-4">
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          {t('register.securityNotice')}
        </AlertDescription>
      </Alert>

      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="terms"
            checked={acceptTerms}
            onCheckedChange={(checked) => setAcceptTerms(checked === true)}
          />
          <Label htmlFor="terms" className="text-sm">
            {t('register.agreeTerms')}{' '}
            <Link to="/terms" className="text-primary hover:underline">
              {t('register.termsOfService')}
            </Link>
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="privacy"
            checked={acceptPrivacy}
            onCheckedChange={(checked) => setAcceptPrivacy(checked === true)}
          />
          <Label htmlFor="privacy" className="text-sm">
            {t('register.agreePrivacy')}{' '}
            <Link to="/privacy" className="text-primary hover:underline">
              {t('register.privacyPolicy')}
            </Link>
          </Label>
        </div>
      </div>
    </div>
  );
};

export default TermsSection;
