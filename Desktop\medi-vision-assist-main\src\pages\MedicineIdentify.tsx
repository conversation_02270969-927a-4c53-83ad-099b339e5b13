
import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import ImageUpload from '@/components/ImageUpload';
import MedicineSearch from '@/components/MedicineSearch';
import MedicineDescription from '@/components/MedicineDescription';
import MedicineResults from '@/components/MedicineResults';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTranslation } from '@/hooks/useTranslation';

const MedicineIdentify = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const method = searchParams.get('method') || 'photo';
  const [identificationResult, setIdentificationResult] = useState(null);

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold text-center mb-8">{t('identify.title')}</h1>
        
        <Tabs value={method} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="photo" className="text-sm">{t('identify.uploadPhoto')}</TabsTrigger>
            <TabsTrigger value="search" className="text-sm">{t('identify.searchByName')}</TabsTrigger>
            <TabsTrigger value="description" className="text-sm">{t('identify.describeMedicine')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="photo">
            <ImageUpload onResult={setIdentificationResult} />
          </TabsContent>
          
          <TabsContent value="search">
            <MedicineSearch onResult={setIdentificationResult} />
          </TabsContent>
          
          <TabsContent value="description">
            <MedicineDescription onResult={setIdentificationResult} />
          </TabsContent>
        </Tabs>

        {identificationResult && (
          <div className="mt-8">
            <MedicineResults result={identificationResult} />
          </div>
        )}
      </div>
    </div>
  );
};

export default MedicineIdentify;
