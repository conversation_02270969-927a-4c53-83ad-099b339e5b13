export const translations = {
  en: {
    // Navigation
    nav: {
      identifyMedicine: 'Identify Medicine',
      aiAssistant: 'AI Assistant',
      dashboard: 'Dashboard',
      login: 'Login',
      register: 'Register'
    },
    // Hero section
    hero: {
      title: 'Identify Any Medicine with AI',
      subtitle: 'Take a photo, get instant results. Safe, accurate, and reliable medicine identification powered by advanced AI technology.',
      getStarted: 'Get Started',
      learnMore: 'Learn More'
    },
    // Features
    features: {
      photoIdentification: 'Photo Identification',
      photoDescription: 'Simply take a photo of your medicine and get instant identification with detailed information about usage and effects.',
      aiAssistant: 'AI Assistant',
      aiDescription: 'Get personalized advice and answers to your medication questions from our intelligent AI assistant.',
      secureHistory: 'Secure History',
      historyDescription: 'Keep track of your identified medicines in a secure, encrypted dashboard accessible only to you.'
    },
    // Medicine Identify
    identify: {
      title: 'Identify Your Medicine',
      uploadPhoto: 'Upload Photo',
      searchByName: 'Search by Name',
      describeMedicine: 'Describe Medicine',
      uploadInstruction: 'Take a clear photo of your medicine or upload from gallery',
      searchPlaceholder: 'Enter medicine name...',
      search: 'Search',
      selectShape: 'Select Shape',
      selectColor: 'Select Color',
      selectSize: 'Select Size',
      pill: 'Pill',
      tablet: 'Tablet',
      capsule: 'Capsule',
      liquid: 'Liquid',
      white: 'White',
      blue: 'Blue',
      red: 'Red',
      yellow: 'Yellow',
      green: 'Green',
      small: 'Small',
      medium: 'Medium',
      large: 'Large',
      identify: 'Identify Medicine'
    },
    // Register
    register: {
      title: 'Create Your Account',
      subtitle: 'Join MediScan to safely identify medicines and track your medication history',
      personalInfo: 'Personal Information',
      firstName: 'First Name',
      firstNamePlaceholder: 'Enter your first name',
      lastName: 'Last Name',
      lastNamePlaceholder: 'Enter your last name',
      email: 'Email Address',
      emailPlaceholder: 'Enter your email',
      dateOfBirth: 'Date of Birth',
      preferredLanguage: 'Preferred Language',
      securityInfo: 'Security Information',
      password: 'Password',
      passwordPlaceholder: 'Create a strong password',
      confirmPassword: 'Confirm Password',
      confirmPasswordPlaceholder: 'Confirm your password',
      passwordRequirement: 'Use 8+ characters with uppercase, lowercase, numbers, and symbols',
      passwordMismatch: 'Passwords do not match',
      weak: 'Weak',
      medium: 'Medium',
      strong: 'Strong',
      securityNotice: 'Your medical data is encrypted and stored securely. We never share your personal information with third parties.',
      agreeTerms: 'I agree to the',
      termsOfService: 'Terms of Service',
      agreePrivacy: 'I agree to the',
      privacyPolicy: 'Privacy Policy',
      createAccount: 'Create Account',
      creatingAccount: 'Creating Account...',
      alreadyHaveAccount: 'Already have an account?',
      signInHere: 'Sign in here'
    },
    // Login
    login: {
      title: 'Welcome Back',
      subtitle: 'Sign in to your MediScan account to continue identifying medicines safely',
      emailPlaceholder: 'Enter your email',
      passwordPlaceholder: 'Enter your password',
      rememberMe: 'Remember me',
      forgotPassword: 'Forgot your password?',
      signIn: 'Sign In',
      signingIn: 'Signing in...',
      noAccount: "Don't have an account?",
      registerHere: 'Register here',
      emailHelp: 'Use the email address associated with your account',
      showPassword: 'Show password',
      hidePassword: 'Hide password',
      needHelp: 'Need help accessing your account?',
      accessibilitySupport: 'Accessibility Support',
      contactSupport: 'Contact Support',
      createOne: 'Create one now'
    },
    // Dashboard
    dashboard: {
      title: 'My Dashboard',
      subtitle: 'Manage your medicine identification history and preferences',
      identifyByPhoto: 'Identify by Photo',
      uploadMedicineImage: 'Upload medicine image',
      searchByName: 'Search by Name',
      findMedicineInfo: 'Find medicine info',
      aiAssistant: 'AI Assistant',
      chatForGuidance: 'Chat for guidance',
      recentSearches: 'Recent Searches',
      recentSearchesDescription: 'Your latest medicine identification attempts',
      viewAllHistory: 'View All History',
      yourStats: 'Your Stats',
      totalSearches: 'Total Searches',
      successRate: 'Success Rate',
      savedMedicines: 'Saved Medicines',
      memberSince: 'Member Since',
      privacySecurity: 'Privacy & Security',
      dataEncryption: 'Data Encryption',
      enabled: 'Enabled',
      searchHistory: 'Search History',
      private: 'Private',
      twoFactorAuth: 'Two-Factor Auth',
      setupRequired: 'Setup Required',
      privacySettings: 'Privacy Settings',
      accessibilityOptions: 'Accessibility Options',
      enableLargeText: 'Enable Large Text Mode',
      highContrast: 'High Contrast Mode',
      screenReader: 'Screen Reader Support',
      match: 'Match'
    },
    // How It Works
    howItWorks: {
      title: 'How MediScan Works',
      step1: {
        title: '1. Upload Photo',
        description: 'Take a clear photo of your medicine or upload from gallery'
      },
      step2: {
        title: '2. AI Analysis',
        description: 'Our AI analyzes shape, color, and text to identify your medicine'
      },
      step3: {
        title: '3. Get Results',
        description: 'Receive detailed information about dosage, effects, and alternatives'
      }
    },
    // Trust Section
    trust: {
      title: 'Trusted by Healthcare Professionals',
      activeUsers: 'Active Users',
      accuracyRate: 'Accuracy Rate',
      languagesSupported: 'Languages Supported'
    },
    // Not Found
    notFound: {
      title: '404',
      message: 'Oops! Page not found',
      returnHome: 'Return to Home'
    },
    // Common
    common: {
      english: 'English',
      french: 'Français',
      german: 'Deutsch'
    },
    // Chat Assistant
    chat: {
      title: 'MediScan AI Assistant',
      welcome: "Hello! I'm your MediScan AI Assistant. I'm here to help you identify your medicine and answer questions about medication safety. How can I assist you today?",
      placeholder: 'Ask me about your medicine...',
      suggestions: {
        photoId: 'Help me identify a medicine from a photo',
        searchName: 'I want to search by medicine name',
        sideEffects: 'Tell me about potential side effects',
        dosage: 'How should I take this medicine?',
        uploadPhoto: 'Upload a photo',
        searchByName: 'Search by name',
        describeMedicine: 'Describe my medicine',
        askSafety: 'Ask about safety',
        takeToUpload: 'Take me to photo upload',
        blurryPhoto: 'What if my photo is blurry?',
        otherMethods: 'Tell me about other identification methods',
        searchMedicine: 'Search by medicine name',
        severeSideEffects: 'What are severe side effects?',
        missedDose: 'What if I missed a dose?',
        cutPills: 'Can I cut pills in half?',
        organizeMedicines: 'Help me organize my medicines',
        checkInteractions: 'Check for drug interactions',
        enableAccessibility: 'Enable accessibility features'
      },
      responses: {
        appPurpose: "Sure! Our app is designed to help users quickly identify any medicine just by uploading a photo, typing its name, or entering a short description. Whether you have a blurry label, only part of the name, or just some basic info — the app will find the correct medicine for you.\n\nIt's especially useful when you're unsure about how a medicine should be used, its dosage, or what it's for. The app provides detailed information, including usage instructions, side effects, and even suggests similar alternatives that perform the same function.\n\nIn short, we help eliminate confusion and give you confidence when dealing with any medication — whether you're a patient, a caregiver, or just someone trying to understand what a certain medicine is.\n\nIf you have any more questions or want to see it in action, I'd be happy to guide you!",
        photoHelp: "I'd be happy to help you identify your medicine from a photo! Here's how to get the best results:\n\n1. **Good lighting** - Take the photo in bright, natural light\n2. **Clear focus** - Make sure the medicine is in sharp focus\n3. **Include text** - Capture any numbers, letters, or symbols on the pill\n4. **Flat surface** - Place the medicine on a contrasting background\n\nWould you like me to guide you to the photo upload page?",
        sideEffects: "Side effects can vary greatly depending on the specific medicine. Here's what you should know:\n\n**Common types of side effects:**\n• Digestive issues (nausea, stomach upset)\n• Drowsiness or dizziness\n• Headaches\n• Skin reactions\n\n**Important:** If you experience severe side effects, stop taking the medicine and contact your healthcare provider immediately.\n\nTo get specific side effect information, I'll need to know which medicine you're asking about. Can you tell me the name or upload a photo?",
        dosage: "Dosage is crucial for medication safety! Here are the key principles:\n\n**Always follow:**\n• Your doctor's prescription exactly\n• Package instructions for over-the-counter medicines\n• Timing recommendations (with/without food, time of day)\n\n**Never:**\n• Exceed the recommended dose\n• Share prescription medicines\n• Stop abruptly without consulting your doctor\n\nFor specific dosage information, I can help you identify your medicine first. Would you like to do that?",
        elderly: "I'm designed to be extra helpful for elderly users! Here are some special considerations:\n\n**For medication safety:**\n• Keep an updated list of all medications\n• Use pill organizers for complex regimens\n• Be aware that metabolism changes with age\n• Watch for interactions between multiple medicines\n\n**Using MediScan:**\n• Voice commands are available (ask me to enable them)\n• Large text mode for better visibility\n• Simple step-by-step guidance\n\nHow can I best assist you with your medication needs?",
        default: "I understand you need help with medication identification or information. I can assist you in several ways:\n\n• **Photo identification** - Upload a picture of your medicine\n• **Name search** - Search our database by medicine name\n• **Description matching** - Describe your medicine's appearance\n• **Safety information** - Get dosage and side effect details\n• **General guidance** - Ask questions about medication safety\n\nWhat would be most helpful for you right now?"
      },
      quickActions: {
        photoId: 'Photo ID',
        nameSearch: 'Name Search',
        describe: 'Describe'
      }
    }
  },
  fr: {
    // Navigation
    nav: {
      identifyMedicine: 'Identifier Médicament',
      aiAssistant: 'Assistant IA',
      dashboard: 'Tableau de bord',
      login: 'Connexion',
      register: 'Inscription'
    },
    // Hero section
    hero: {
      title: 'Identifiez tout médicament avec l\'IA',
      subtitle: 'Prenez une photo, obtenez des résultats instantanés. Identification de médicaments sûre, précise et fiable alimentée par une technologie IA avancée.',
      getStarted: 'Commencer',
      learnMore: 'En savoir plus'
    },
    // Features
    features: {
      photoIdentification: 'Identification par photo',
      photoDescription: 'Prenez simplement une photo de votre médicament et obtenez une identification instantanée avec des informations détaillées sur l\'utilisation et les effets.',
      aiAssistant: 'Assistant IA',
      aiDescription: 'Obtenez des conseils personnalisés et des réponses à vos questions sur les médicaments grâce à notre assistant IA intelligent.',
      secureHistory: 'Historique sécurisé',
      historyDescription: 'Gardez une trace de vos médicaments identifiés dans un tableau de bord sécurisé et crypté accessible uniquement à vous.'
    },
    // Medicine Identify
    identify: {
      title: 'Identifiez votre médicament',
      uploadPhoto: 'Télécharger une photo',
      searchByName: 'Rechercher par nom',
      describeMedicine: 'Décrire le médicament',
      uploadInstruction: 'Prenez une photo claire de votre médicament ou téléchargez depuis la galerie',
      searchPlaceholder: 'Entrez le nom du médicament...',
      search: 'Rechercher',
      selectShape: 'Sélectionner la forme',
      selectColor: 'Sélectionner la couleur',
      selectSize: 'Sélectionner la taille',
      pill: 'Pilule',
      tablet: 'Comprimé',
      capsule: 'Gélule',
      liquid: 'Liquide',
      white: 'Blanc',
      blue: 'Bleu',
      red: 'Rouge',
      yellow: 'Jaune',
      green: 'Vert',
      small: 'Petit',
      medium: 'Moyen',
      large: 'Grand',
      identify: 'Identifier le médicament'
    },
    // Register
    register: {
      title: 'Créez votre compte',
      subtitle: 'Rejoignez MediScan pour identifier en toute sécurité les médicaments et suivre votre historique de médicaments',
      personalInfo: 'Informations personnelles',
      firstName: 'Prénom',
      firstNamePlaceholder: 'Entrez votre prénom',
      lastName: 'Nom de famille',
      lastNamePlaceholder: 'Entrez votre nom de famille',
      email: 'Adresse e-mail',
      emailPlaceholder: 'Entrez votre e-mail',
      dateOfBirth: 'Date de naissance',
      preferredLanguage: 'Langue préférée',
      securityInfo: 'Informations de sécurité',
      password: 'Mot de passe',
      passwordPlaceholder: 'Créez un mot de passe fort',
      confirmPassword: 'Confirmer le mot de passe',
      confirmPasswordPlaceholder: 'Confirmez votre mot de passe',
      passwordRequirement: 'Utilisez 8+ caractères avec majuscules, minuscules, chiffres et symboles',
      passwordMismatch: 'Les mots de passe ne correspondent pas',
      weak: 'Faible',
      medium: 'Moyen',
      strong: 'Fort',
      securityNotice: 'Vos données médicales sont cryptées et stockées en toute sécurité. Nous ne partageons jamais vos informations personnelles avec des tiers.',
      agreeTerms: 'J\'accepte les',
      termsOfService: 'Conditions d\'utilisation',
      agreePrivacy: 'J\'accepte la',
      privacyPolicy: 'Politique de confidentialité',
      createAccount: 'Créer un compte',
      creatingAccount: 'Création du compte...',
      alreadyHaveAccount: 'Vous avez déjà un compte ?',
      signInHere: 'Connectez-vous ici'
    },
    // Login
    login: {
      title: 'Bon retour',
      subtitle: 'Connectez-vous à votre compte MediScan pour continuer à identifier les médicaments en toute sécurité',
      emailPlaceholder: 'Entrez votre e-mail',
      passwordPlaceholder: 'Entrez votre mot de passe',
      rememberMe: 'Se souvenir de moi',
      forgotPassword: 'Mot de passe oublié ?',
      signIn: 'Se connecter',
      signingIn: 'Connexion en cours...',
      noAccount: 'Vous n\'avez pas de compte ?',
      registerHere: 'Inscrivez-vous ici',
      emailHelp: 'Utilisez l\'adresse e-mail associée à votre compte',
      showPassword: 'Afficher le mot de passe',
      hidePassword: 'Masquer le mot de passe',
      needHelp: 'Besoin d\'aide pour accéder à votre compte ?',
      accessibilitySupport: 'Support d\'accessibilité',
      contactSupport: 'Contacter le support',
      createOne: 'Créez-en un maintenant'
    },
    // Dashboard
    dashboard: {
      title: 'Mon tableau de bord',
      subtitle: 'Gérez votre historique d\'identification de médicaments et vos préférences',
      identifyByPhoto: 'Identifier par photo',
      uploadMedicineImage: 'Télécharger une image de médicament',
      searchByName: 'Rechercher par nom',
      findMedicineInfo: 'Trouver des informations sur les médicaments',
      aiAssistant: 'Assistant IA',
      chatForGuidance: 'Discuter pour obtenir des conseils',
      recentSearches: 'Recherches récentes',
      recentSearchesDescription: 'Vos dernières tentatives d\'identification de médicaments',
      viewAllHistory: 'Voir tout l\'historique',
      yourStats: 'Vos statistiques',
      totalSearches: 'Recherches totales',
      successRate: 'Taux de réussite',
      savedMedicines: 'Médicaments sauvegardés',
      memberSince: 'Membre depuis',
      privacySecurity: 'Confidentialité et sécurité',
      dataEncryption: 'Chiffrement des données',
      enabled: 'Activé',
      searchHistory: 'Historique de recherche',
      private: 'Privé',
      twoFactorAuth: 'Authentification à deux facteurs',
      setupRequired: 'Configuration requise',
      privacySettings: 'Paramètres de confidentialité',
      accessibilityOptions: 'Options d\'accessibilité',
      enableLargeText: 'Activer le mode grand texte',
      highContrast: 'Mode contraste élevé',
      screenReader: 'Support de lecteur d\'écran',
      match: 'Correspondance'
    },
    // How It Works
    howItWorks: {
      title: 'Comment fonctionne MediScan',
      step1: {
        title: '1. Télécharger une photo',
        description: 'Prenez une photo claire de votre médicament ou téléchargez depuis la galerie'
      },
      step2: {
        title: '2. Analyse IA',
        description: 'Notre IA analyse la forme, la couleur et le texte pour identifier votre médicament'
      },
      step3: {
        title: '3. Obtenir les résultats',
        description: 'Recevez des informations détaillées sur le dosage, les effets et les alternatives'
      }
    },
    // Trust Section
    trust: {
      title: 'Approuvé par les professionnels de la santé',
      activeUsers: 'Utilisateurs actifs',
      accuracyRate: 'Taux de précision',
      languagesSupported: 'Langues prises en charge'
    },
    // Not Found
    notFound: {
      title: '404',
      message: 'Oups ! Page introuvable',
      returnHome: 'Retour à l\'accueil'
    },
    // Common
    common: {
      english: 'English',
      french: 'Français',
      german: 'Deutsch'
    },
    // Chat Assistant
    chat: {
      title: 'Assistant IA MediScan',
      welcome: "Bonjour ! Je suis votre assistant IA MediScan. Je suis là pour vous aider à identifier vos médicaments et répondre à vos questions sur la sécurité des médicaments. Comment puis-je vous aider aujourd'hui ?",
      placeholder: 'Posez-moi une question sur vos médicaments...',
      suggestions: {
        photoId: 'Aidez-moi à identifier un médicament à partir d\'une photo',
        searchName: 'Je veux rechercher par nom de médicament',
        sideEffects: 'Parlez-moi des effets secondaires potentiels',
        dosage: 'Comment dois-je prendre ce médicament ?',
        uploadPhoto: 'Télécharger une photo',
        searchByName: 'Rechercher par nom',
        describeMedicine: 'Décrire mon médicament',
        askSafety: 'Demander sur la sécurité',
        takeToUpload: 'Amenez-moi au téléchargement de photo',
        blurryPhoto: 'Et si ma photo est floue ?',
        otherMethods: 'Parlez-moi d\'autres méthodes d\'identification',
        searchMedicine: 'Rechercher par nom de médicament',
        severeSideEffects: 'Quels sont les effets secondaires graves ?',
        missedDose: 'Et si j\'ai oublié une dose ?',
        cutPills: 'Puis-je couper les pilules en deux ?',
        organizeMedicines: 'Aidez-moi à organiser mes médicaments',
        checkInteractions: 'Vérifier les interactions médicamenteuses',
        enableAccessibility: 'Activer les fonctionnalités d\'accessibilité'
      },
      responses: {
        appPurpose: "Bien sûr ! Notre application est conçue pour aider les utilisateurs à identifier rapidement tout médicament en téléchargeant simplement une photo, en tapant son nom ou en entrant une courte description. Que vous ayez une étiquette floue, seulement une partie du nom, ou juste quelques informations de base — l'application trouvera le bon médicament pour vous.\n\nC'est particulièrement utile lorsque vous n'êtes pas sûr de la façon dont un médicament doit être utilisé, de son dosage ou de son utilité. L'application fournit des informations détaillées, y compris les instructions d'utilisation, les effets secondaires, et suggère même des alternatives similaires qui remplissent la même fonction.\n\nEn bref, nous aidons à éliminer la confusion et vous donnons confiance lors de la manipulation de tout médicament — que vous soyez un patient, un soignant, ou simplement quelqu'un qui essaie de comprendre ce qu'est un certain médicament.\n\nSi vous avez d'autres questions ou voulez le voir en action, je serais ravi de vous guider !",
        photoHelp: "Je serais ravi de vous aider à identifier votre médicament à partir d'une photo ! Voici comment obtenir les meilleurs résultats :\n\n1. **Bon éclairage** - Prenez la photo sous une lumière vive et naturelle\n2. **Mise au point claire** - Assurez-vous que le médicament est net\n3. **Inclure le texte** - Capturez tous les chiffres, lettres ou symboles sur la pilule\n4. **Surface plane** - Placez le médicament sur un arrière-plan contrasté\n\nVoulez-vous que je vous guide vers la page de téléchargement de photo ?",
        sideEffects: "Les effets secondaires peuvent varier considérablement selon le médicament spécifique. Voici ce que vous devez savoir :\n\n**Types courants d'effets secondaires :**\n• Problèmes digestifs (nausées, maux d'estomac)\n• Somnolence ou étourdissements\n• Maux de tête\n• Réactions cutanées\n\n**Important :** Si vous ressentez des effets secondaires graves, arrêtez de prendre le médicament et contactez immédiatement votre professionnel de la santé.\n\nPour obtenir des informations spécifiques sur les effets secondaires, j'aurai besoin de savoir de quel médicament vous parlez. Pouvez-vous me dire le nom ou télécharger une photo ?",
        dosage: "Le dosage est crucial pour la sécurité des médicaments ! Voici les principes clés :\n\n**Suivez toujours :**\n• La prescription de votre médecin exactement\n• Les instructions de l'emballage pour les médicaments en vente libre\n• Les recommandations de timing (avec/without food, moment de la journée)\n\n**Jamais :**\n• Dépasser la dose recommandée\n• Partager des médicaments sur ordonnance\n• Arrêter brusquement sans consulter votre médecin\n\nPour des informations de dosage spécifiques, je peux vous aider à identifier votre médicament d'abord. Voulez-vous faire cela ?",
        elderly: "Je suis conçu pour être particulièrement utile aux utilisateurs âgés ! Voici quelques considérations spéciales :\n\n**Pour la sécurité des médicaments :**\n• Tenez une liste à jour de tous les médicaments\n• Utilisez des organisateurs de pilules pour les régimes complexes\n• Sachez que le métabolisme change avec l'âge\n• Surveillez les interactions entre plusieurs médicaments\n\n**Utilisation de MediScan :**\n• Les commandes vocales sont disponibles (demandez-moi de les activer)\n• Mode grand texte pour une meilleure visibilité\n• Conseils simples étape par étape\n\nComment puis-je vous aider au mieux avec vos besoins en médicaments ?",
        default: "Je comprends que vous avez besoin d'aide pour l'identification ou l'information sur les médicaments. Je peux vous aider de plusieurs façons :\n\n• **Identification par photo** - Téléchargez une image de votre médicament\n• **Recherche par nom** - Recherchez dans notre base de données par nom de médicament\n• **Correspondance de description** - Décrivez l'apparence de votre médicament\n• **Informations de sécurité** - Obtenez des détails sur le dosage et les effets secondaires\n• **Conseils généraux** - Posez des questions sur la sécurité des médicaments\n\nQue serait le plus utile pour vous en ce moment ?"
      },
      quickActions: {
        photoId: 'ID Photo',
        nameSearch: 'Recherche Nom',
        describe: 'Décrire'
      }
    }
  },
  de: {
    // Navigation
    nav: {
      identifyMedicine: 'Medikament identifizieren',
      aiAssistant: 'KI-Assistent',
      dashboard: 'Dashboard',
      login: 'Anmelden',
      register: 'Registrieren'
    },
    // Hero section
    hero: {
      title: 'Jedes Medikament mit KI identifizieren',
      subtitle: 'Machen Sie ein Foto, erhalten Sie sofortige Ergebnisse. Sichere, genaue und zuverlässige Medikamentenidentifikation mit fortschrittlicher KI-Technologie.',
      getStarted: 'Loslegen',
      learnMore: 'Mehr erfahren'
    },
    // Features
    features: {
      photoIdentification: 'Foto-Identifikation',
      photoDescription: 'Machen Sie einfach ein Foto Ihres Medikaments und erhalten Sie sofortige Identifikation mit detaillierten Informationen über Verwendung und Wirkungen.',
      aiAssistant: 'KI-Assistent',
      aiDescription: 'Erhalten Sie personalisierte Beratung und Antworten auf Ihre Medikamentenfragen von unserem intelligenten KI-Assistenten.',
      secureHistory: 'Sichere Historie',
      historyDescription: 'Behalten Sie den Überblick über Ihre identifizierten Medikamente in einem sicheren, verschlüsselten Dashboard, das nur für Sie zugänglich ist.'
    },
    // Medicine Identify
    identify: {
      title: 'Identifizieren Sie Ihr Medikament',
      uploadPhoto: 'Foto hochladen',
      searchByName: 'Nach Name suchen',
      describeMedicine: 'Medikament beschreiben',
      uploadInstruction: 'Machen Sie ein klares Foto Ihres Medikaments oder laden Sie es aus der Galerie hoch',
      searchPlaceholder: 'Medikamentennamen eingeben...',
      search: 'Suchen',
      selectShape: 'Form auswählen',
      selectColor: 'Farbe auswählen',
      selectSize: 'Größe auswählen',
      pill: 'Pille',
      tablet: 'Tablette',
      capsule: 'Kapsel',
      liquid: 'Flüssigkeit',
      white: 'Weiß',
      blue: 'Blau',
      red: 'Rot',
      yellow: 'Gelb',
      green: 'Grün',
      small: 'Klein',
      medium: 'Mittel',
      large: 'Groß',
      identify: 'Medikament identifizieren'
    },
    // Register
    register: {
      title: 'Erstellen Sie Ihr Konto',
      subtitle: 'Treten Sie MediScan bei, um Medikamente sicher zu identifizieren und Ihre Medikamentenhistorie zu verfolgen',
      personalInfo: 'Persönliche Informationen',
      firstName: 'Vorname',
      firstNamePlaceholder: 'Geben Sie Ihren Vornamen ein',
      lastName: 'Nachname',
      lastNamePlaceholder: 'Geben Sie Ihren Nachnamen ein',
      email: 'E-Mail-Adresse',
      emailPlaceholder: 'Geben Sie Ihre E-Mail ein',
      dateOfBirth: 'Geburtsdatum',
      preferredLanguage: 'Bevorzugte Sprache',
      securityInfo: 'Sicherheitsinformationen',
      password: 'Passwort',
      passwordPlaceholder: 'Erstellen Sie ein starkes Passwort',
      confirmPassword: 'Passwort bestätigen',
      confirmPasswordPlaceholder: 'Bestätigen Sie Ihr Passwort',
      passwordRequirement: 'Verwenden Sie 8+ Zeichen mit Groß-, Kleinbuchstaben, Zahlen und Symbolen',
      passwordMismatch: 'Passwörter stimmen nicht überein',
      weak: 'Schwach',
      medium: 'Mittel',
      strong: 'Stark',
      securityNotice: 'Ihre medizinischen Daten werden verschlüsselt und sicher gespeichert. Wir teilen Ihre persönlichen Informationen niemals mit Dritten.',
      agreeTerms: 'Ich stimme den',
      termsOfService: 'Nutzungsbedingungen',
      agreePrivacy: 'Ich stimme der',
      privacyPolicy: 'Datenschutzrichtlinie',
      createAccount: 'Konto erstellen',
      creatingAccount: 'Konto wird erstellt...',
      alreadyHaveAccount: 'Haben Sie bereits ein Konto?',
      signInHere: 'Hier anmelden'
    },
    // Login
    login: {
      title: 'Willkommen zurück',
      subtitle: 'Melden Sie sich bei Ihrem MediScan-Konto an, um Medikamente weiterhin sicher zu identifizieren',
      emailPlaceholder: 'Geben Sie Ihre E-Mail ein',
      passwordPlaceholder: 'Geben Sie Ihr Passwort ein',
      rememberMe: 'Angemeldet bleiben',
      forgotPassword: 'Passwort vergessen?',
      signIn: 'Anmelden',
      signingIn: 'Anmeldung läuft...',
      noAccount: 'Haben Sie kein Konto?',
      registerHere: 'Hier registrieren',
      emailHelp: 'Verwenden Sie die E-Mail-Adresse, die mit Ihrem Konto verknüpft ist',
      showPassword: 'Passwort anzeigen',
      hidePassword: 'Passwort verbergen',
      needHelp: 'Benötigen Sie Hilfe beim Zugriff auf Ihr Konto?',
      accessibilitySupport: 'Barrierefreiheits-Support',
      contactSupport: 'Support kontaktieren',
      createOne: 'Jetzt erstellen'
    },
    // Dashboard
    dashboard: {
      title: 'Mein Dashboard',
      subtitle: 'Verwalten Sie Ihre Medikamentenidentifikationshistorie und Einstellungen',
      identifyByPhoto: 'Per Foto identifizieren',
      uploadMedicineImage: 'Medikamentenbild hochladen',
      searchByName: 'Nach Name suchen',
      findMedicineInfo: 'Medikamenteninformationen finden',
      aiAssistant: 'KI-Assistent',
      chatForGuidance: 'Chatten für Anleitung',
      recentSearches: 'Letzte Suchen',
      recentSearchesDescription: 'Ihre letzten Medikamentenidentifikationsversuche',
      viewAllHistory: 'Gesamte Historie anzeigen',
      yourStats: 'Ihre Statistiken',
      totalSearches: 'Gesamtsuchen',
      successRate: 'Erfolgsrate',
      savedMedicines: 'Gespeicherte Medikamente',
      memberSince: 'Mitglied seit',
      privacySecurity: 'Datenschutz & Sicherheit',
      dataEncryption: 'Datenverschlüsselung',
      enabled: 'Aktiviert',
      searchHistory: 'Suchhistorie',
      private: 'Privat',
      twoFactorAuth: 'Zwei-Faktor-Authentifizierung',
      setupRequired: 'Einrichtung erforderlich',
      privacySettings: 'Datenschutzeinstellungen',
      accessibilityOptions: 'Barrierefreiheitsoptionen',
      enableLargeText: 'Großtext-Modus aktivieren',
      highContrast: 'Hoher Kontrast-Modus',
      screenReader: 'Bildschirmleser-Unterstützung',
      match: 'Übereinstimmung'
    },
    // How It Works
    howItWorks: {
      title: 'Wie MediScan funktioniert',
      step1: {
        title: '1. Foto hochladen',
        description: 'Machen Sie ein klares Foto Ihres Medikaments oder laden Sie es aus der Galerie hoch'
      },
      step2: {
        title: '2. KI-Analyse',
        description: 'Unsere KI analysiert Form, Farbe und Text, um Ihr Medikament zu identifizieren'
      },
      step3: {
        title: '3. Ergebnisse erhalten',
        description: 'Erhalten Sie detaillierte Informationen über Dosierung, Wirkungen und Alternativen'
      }
    },
    // Trust Section
    trust: {
      title: 'Vertraut von Gesundheitsfachkräften',
      activeUsers: 'Aktive Benutzer',
      accuracyRate: 'Genauigkeitsrate',
      languagesSupported: 'Unterstützte Sprachen'
    },
    // Not Found
    notFound: {
      title: '404',
      message: 'Ups! Seite nicht gefunden',
      returnHome: 'Zur Startseite zurückkehren'
    },
    // Common
    common: {
      english: 'English',
      french: 'Français',
      german: 'Deutsch'
    },
    // Chat Assistant
    chat: {
      title: 'MediScan KI-Assistent',
      welcome: "Hallo! Ich bin Ihr MediScan KI-Assistent. Ich bin hier, um Ihnen bei der Identifikation Ihrer Medikamente zu helfen und Fragen zur Medikamentensicherheit zu beantworten. Wie kann ich Ihnen heute helfen?",
      placeholder: 'Fragen Sie mich nach Ihren Medikamenten...',
      suggestions: {
        photoId: 'Helfen Sie mir, ein Medikament anhand eines Fotos zu identifizieren',
        searchName: 'Ich möchte nach Medikamentennamen suchen',
        sideEffects: 'Erzählen Sie mir von möglichen Nebenwirkungen',
        dosage: 'Wie soll ich dieses Medikament einnehmen?',
        uploadPhoto: 'Foto hochladen',
        searchByName: 'Nach Name suchen',
        describeMedicine: 'Mein Medikament beschreiben',
        askSafety: 'Nach Sicherheit fragen',
        takeToUpload: 'Bringen Sie mich zum Foto-Upload',
        blurryPhoto: 'Was ist, wenn mein Foto unscharf ist?',
        otherMethods: 'Erzählen Sie mir von anderen Identifikationsmethoden',
        searchMedicine: 'Nach Medikamentennamen suchen',
        severeSideEffects: 'Was sind schwere Nebenwirkungen?',
        missedDose: 'Was ist, wenn ich eine Dosis vergessen habe?',
        cutPills: 'Kann ich Pillen halbieren?',
        organizeMedicines: 'Helfen Sie mir, meine Medikamente zu organisieren',
        checkInteractions: 'Wechselwirkungen überprüfen',
        enableAccessibility: 'Barrierefreiheitsfunktionen aktivieren'
      },
      responses: {
        appPurpose: "Sicher! Unsere App ist darauf ausgelegt, Benutzern zu helfen, schnell jedes Medikament zu identifizieren, indem sie einfach ein Foto hochladen, seinen Namen eingeben oder eine kurze Beschreibung eingeben. Ob Sie ein unscharfes Etikett haben, nur einen Teil des Namens oder nur einige grundlegende Informationen — die App wird das richtige Medikament für Sie finden.\n\nEs ist besonders nützlich, wenn Sie sich nicht sicher sind, wie ein Medikament verwendet werden soll, welche Dosierung es hat oder wofür es ist. Die App bietet detaillierte Informationen, einschließlich Anwendungshinweise, Nebenwirkungen und suggert sogar ähnliche Alternativen, die dieselbe Funktion erfüllen.\n\nKurz gesagt, wir helfen dabei, Verwirrung zu beseitigen und geben Ihnen Vertrauen im Umgang mit jeder Medikation — ob Sie ein Patient, eine Pflegekraft oder einfach jemand sind, der versucht zu verstehen, was ein bestimmtes Medikament ist.\n\nWenn Sie weitere Fragen haben oder es in Aktion sehen möchten, helfe ich Ihnen gerne weiter!",
        photoHelp: "Gerne helfe ich Ihnen dabei, Ihr Medikament anhand eines Fotos zu identifizieren! So erhalten Sie die besten Ergebnisse:\n\n1. **Gute Beleuchtung** - Machen Sie das Foto bei hellem, natürlichem Licht\n2. **Klare Schärfe** - Stellen Sie sicher, dass das Medikament scharf fokussiert ist\n3. **Text einbeziehen** - Erfassen Sie alle Zahlen, Buchstaben oder Symbole auf der Pille\n4. **Flache Oberfläche** - Platzieren Sie das Medikament auf einem kontrastierenden Hintergrund\n\nSoll ich Sie zur Foto-Upload-Seite führen?",
        sideEffects: "Nebenwirkungen können je nach spezifischem Medikament stark variieren. Das sollten Sie wissen:\n\n**Häufige Arten von Nebenwirkungen:**\n• Verdauungsprobleme (Übelkeit, Magenverstimmung)\n• Schläfrigkeit oder Schwindel\n• Kopfschmerzen\n• Hautreaktionen\n\n**Wichtig:** Wenn Sie schwere Nebenwirkungen erleben, hören Sie auf, das Medikament zu nehmen und kontaktieren Sie sofort Ihren Arzt.\n\nUm spezifische Nebenwirkungsinformationen zu erhalten, muss ich wissen, über welches Medikament Sie sprechen. Können Sie mir den Namen sagen oder ein Foto hochladen?",
        dosage: "Die Dosierung ist entscheidend für die Medikamentensicherheit! Hier sind die wichtigsten Prinzipien:\n\n**Befolgen Sie immer:**\n• Das Rezept Ihres Arztes genau\n• Packungsanweisungen für rezeptfreie Medikamente\n• Zeitempfehlungen (mit/ohne Essen, Tageszeit)\n\n**Niemals:**\n• Die empfohlene Dosis überschreiten\n• Verschreibungspflichtige Medikamente teilen\n• Abrupt aufhören ohne Ihren Arzt zu konsultieren\n\nFür spezifische Dosierungsinformationen kann ich Ihnen zuerst bei der Identifikation Ihres Medikaments helfen. Möchten Sie das tun?",
        elderly: "Ich bin darauf ausgelegt, älteren Benutzern besonders zu helfen! Hier sind einige besondere Überlegungen:\n\n**Für Medikamentensicherheit:**\n• Führen Sie eine aktuelle Liste aller Medikamente\n• Verwenden Sie Pillen-Organizer für komplexe Regime\n• Sachez sich bewusst, dass sich der Stoffwechsel mit dem Alter ändert\n• Achten Sie auf Wechselwirkungen zwischen mehreren Medikamenten\n\n**MediScan verwenden:**\n• Sprachbefehle sind verfügbar (fragen Sie mich, sie zu aktivieren)\n• Großtext-Modus für bessere Sichtbarkeit\n• Einfache Schritt-für-Schritt-Anleitung\n\nWie kann ich Ihnen am besten bei Ihren Medikamentenbedürfnissen helfen?",
        default: "Ich verstehe, dass Sie Hilfe bei der Medikamentenidentifikation oder -information benötigen. Ich kann Ihnen auf verschiedene Weise helfen:\n\n• **Foto-Identifikation** - Laden Sie ein Bild Ihres Medikaments hoch\n• **Namenssuche** - Durchsuchen Sie unsere Datenbank nach Medikamentennamen\n• **Beschreibungsabgleich** - Beschreiben Sie das Aussehen Ihres Medikaments\n• **Sicherheitsinformationen** - Erhalten Sie Details zu Dosierung und Nebenwirkungen\n• **Allgemeine Beratung** - Stellen Sie Fragen zur Medikamentensicherheit\n\nWas wäre jetzt am hilfreichsten für Sie?"
      },
      quickActions: {
        photoId: 'Foto-ID',
        nameSearch: 'Namenssuche',
        describe: 'Beschreiben'
      }
    }
  }
};

export type TranslationKey = keyof typeof translations.en;
