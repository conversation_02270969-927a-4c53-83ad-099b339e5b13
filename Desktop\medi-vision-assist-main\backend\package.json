{"name": "medical-chatbot-backend", "version": "1.0.0", "description": "AI-powered medical chatbot backend for medicine identification and guidance", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["medical", "chatbot", "ai", "medicine", "healthcare"], "author": "Medical App Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}