
import React from 'react';
import { CheckCircle, AlertTriangle, Info, Heart, Clock, Shield, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';

interface MedicineResultsProps {
  result: {
    confidence: number;
    medicine: {
      product: string;
      type: string;
      usage: string;
      dosage: string;
      similar: string;
      formatted?: string;
      name?: string;
      genericName?: string;
      brand?: string;
      shape?: string;
      color?: string;
      imprint?: string;
      sideEffects?: string[];
      alternatives?: string[];
    };
    rxcui?: string;
  };
}

const MedicineResults: React.FC<MedicineResultsProps> = ({ result }) => {
  const { confidence, medicine, rxcui } = result;

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'bg-green-500';
    if (confidence >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 90) return 'High Confidence';
    if (confidence >= 70) return 'Medium Confidence';
    return 'Low Confidence - Verify with Healthcare Provider';
  };

  const formatRelatedMedicines = (similar: string) => {
    if (!similar || similar === 'None found' || similar === 'No related medicines found in database') {
      return (
        <p className="text-muted-foreground">
          No related medicines found in our database.
        </p>
      );
    }

    // Split the medicines and clean them up
    const medicines = similar.split(', ').filter(med => med.trim().length > 0);
    
    if (medicines.length === 0) {
      return (
        <p className="text-muted-foreground">
          No related medicines found in our database.
        </p>
      );
    }

    return (
      <div className="space-y-3">
        <div className="grid gap-2">
          {medicines.map((medicine, index) => (
            <div key={index} className="flex items-center space-x-2 p-2 bg-muted/50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
              <span className="text-sm font-medium">{medicine}</span>
            </div>
          ))}
        </div>
        <p className="text-xs text-muted-foreground mt-3">
          These are medicines that contain the same or similar active ingredients. 
          Always consult your pharmacist or healthcare provider before switching medicines.
        </p>
      </div>
    );
  };

  // Check if this is the new RxNorm format or legacy format
  const isRxNormFormat = medicine.product && medicine.type;
  
  if (isRxNormFormat) {
    return (
      <div className="space-y-6">
        {/* Confidence Badge */}
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span className="font-medium">
                Medicine Identification: {getConfidenceText(confidence)}
              </span>
              <Badge className={`${getConfidenceColor(confidence)} text-white`}>
                {confidence}% Match
              </Badge>
            </div>
          </AlertDescription>
        </Alert>

        {/* Medicine Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-primary flex items-center gap-2">
              {medicine.product}
              <Badge variant="outline">{medicine.type}</Badge>
            </CardTitle>
            <CardDescription className="text-lg">
              Official medicine information powered by RxNorm API
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Usage Information */}
            <div>
              <h3 className="font-semibold text-lg mb-3 flex items-center">
                <Heart className="h-5 w-5 mr-2" />
                Medical Use
              </h3>
              <p className="text-muted-foreground leading-relaxed">{medicine.usage}</p>
            </div>

            <Separator />

            {/* Dosage Information */}
            <div>
              <h3 className="font-semibold text-lg mb-3 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Dosage Guidelines
              </h3>
              <p className="text-muted-foreground leading-relaxed">{medicine.dosage}</p>
              <Alert className="mt-4">
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  <strong>Important:</strong> Always follow the dosage instructions on your prescription 
                  or package. Dosages may vary based on individual needs and medical conditions.
                </AlertDescription>
              </Alert>
            </div>

            <Separator />

            {/* Related Medicines */}
            <div>
              <h3 className="font-semibold text-lg mb-3 flex items-center">
                <Info className="h-5 w-5 mr-2" />
                Related Medicines
              </h3>
              {formatRelatedMedicines(medicine.similar)}
            </div>

            {rxcui && (
              <>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">RxNorm Concept ID</p>
                    <p className="text-sm text-muted-foreground">{rxcui}</p>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open(`https://mor.nlm.nih.gov/RxNav/search?searchBy=RXCUI&searchTerm=${rxcui}`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Official Info
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Educational Disclaimer */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Educational Purpose Only:</strong> This information is sourced from official medical databases 
            and is for educational purposes only. It is not a substitute for professional medical advice, 
            diagnosis, or treatment. Always consult with a qualified healthcare provider before making 
            decisions about your medication or health condition.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Legacy format for backward compatibility
  return (
    <div className="space-y-6">
      {/* Confidence Badge */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <span className="font-medium">
              Identification Result: {getConfidenceText(confidence)}
            </span>
            <Badge className={`${getConfidenceColor(confidence)} text-white`}>
              {confidence}% Match
            </Badge>
          </div>
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl text-primary">{medicine.name || medicine.product}</CardTitle>
          {medicine.genericName && (
            <CardDescription className="text-lg">
              Generic: {medicine.genericName}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="space-y-6">
          {(medicine.shape || medicine.color || medicine.imprint) && (
            <div>
              <h3 className="font-semibold text-lg mb-3 flex items-center">
                <Info className="h-5 w-5 mr-2" />
                Physical Description
              </h3>
              <div className="grid md:grid-cols-3 gap-4">
                {medicine.shape && (
                  <div>
                    <p className="font-medium">Shape</p>
                    <p className="text-muted-foreground">{medicine.shape}</p>
                  </div>
                )}
                {medicine.color && (
                  <div>
                    <p className="font-medium">Color</p>
                    <p className="text-muted-foreground">{medicine.color}</p>
                  </div>
                )}
                {medicine.imprint && (
                  <div>
                    <p className="font-medium">Imprint</p>
                    <p className="text-muted-foreground">{medicine.imprint}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {medicine.usage && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Usage & Purpose
                </h3>
                <p className="text-muted-foreground leading-relaxed">{medicine.usage}</p>
              </div>
            </>
          )}

          {medicine.dosage && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Dosage Instructions
                </h3>
                <p className="text-muted-foreground leading-relaxed">{medicine.dosage}</p>
                <Alert className="mt-4">
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Important:</strong> Always follow the dosage instructions on your prescription 
                    or package. Consult your healthcare provider if you have questions.
                  </AlertDescription>
                </Alert>
              </div>
            </>
          )}

          {medicine.sideEffects && medicine.sideEffects.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Possible Side Effects
                </h3>
                <div className="grid md:grid-cols-2 gap-2">
                  {medicine.sideEffects.map((effect, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="text-muted-foreground">{effect}</span>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {medicine.alternatives && medicine.alternatives.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold text-lg mb-3">Alternative Medicines</h3>
                <div className="flex flex-wrap gap-2">
                  {medicine.alternatives.map((alternative, index) => (
                    <Badge key={index} variant="outline" className="text-sm">
                      {alternative}
                    </Badge>
                  ))}
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Consult your pharmacist or healthcare provider before switching medicines.
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Medical Disclaimer:</strong> This information is for educational purposes only 
          and is not a substitute for professional medical advice. Always consult with a healthcare 
          provider before making decisions about your medication.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default MedicineResults;
