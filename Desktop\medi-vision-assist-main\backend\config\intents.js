
module.exports = {
  greet: {
    patterns: [
      'hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening',
      'greetings', 'what\'s up', 'howdy', 'hi there', 'hello there'
    ],
    keywords: ['hi', 'hello', 'hey', 'greetings', 'morning', 'afternoon', 'evening'],
    entities: []
  },

  ask_bot_status: {
    patterns: [
      'how are you', 'what\'s up', 'how do you do', 'how are things',
      'are you working', 'are you online', 'are you there', 'status check'
    ],
    keywords: ['how', 'are', 'you', 'status', 'doing', 'working', 'online', 'there'],
    entities: []
  },

  app_info: {
    patterns: [
      'what is this app', 'what does this do', 'how does this work', 'what can you do',
      'tell me about this app', 'app purpose', 'what is this for', 'how can you help',
      'what are your features', 'what can this app do', 'explain this app'
    ],
    keywords: ['app', 'purpose', 'function', 'help', 'about', 'what', 'how', 'features', 'do', 'work'],
    entities: []
  },

  symptom_query: {
    patterns: [
      'i have a headache', 'my stomach hurts', 'i feel sick', 'i am in pain',
      'something hurts', 'i don\'t feel well', 'i have symptoms', 'i\'m not feeling good',
      'my head hurts', 'stomach pain', 'back pain', 'chest pain', 'sore throat'
    ],
    keywords: ['pain', 'hurt', 'ache', 'sick', 'feel', 'symptom', 'symptoms', 'sore', 'headache', 'fever'],
    entities: ['symptom', 'body_part', 'severity']
  },

  medicine_identification: {
    patterns: [
      'identify this medicine', 'what is this pill', 'unknown medication', 'can you identify',
      'what medicine is this', 'identify pill', 'what drug is this', 'medicine identification',
      'pill identification', 'identify medication', 'what tablet is this'
    ],
    keywords: ['identify', 'medicine', 'pill', 'tablet', 'medication', 'drug', 'capsule', 'what'],
    entities: ['medicine']
  },

  dosage_question: {
    patterns: [
      'how much should i take', 'what is the dosage', 'how often', 'when should i take',
      'how many pills', 'dosage information', 'how much medicine', 'taking instructions',
      'how to take', 'medicine dosage', 'pill dosage'
    ],
    keywords: ['dosage', 'amount', 'how', 'much', 'often', 'take', 'many', 'pills', 'instructions'],
    entities: ['medicine']
  },

  side_effects: {
    patterns: [
      'side effects', 'adverse reactions', 'what are the risks', 'is this safe',
      'any warnings', 'medicine side effects', 'drug reactions', 'safety information',
      'warnings about', 'risks of taking', 'negative effects'
    ],
    keywords: ['side', 'effects', 'reactions', 'safe', 'warnings', 'risks', 'adverse', 'safety'],
    entities: ['medicine']
  },

  emergency: {
    patterns: [
      'emergency', 'urgent', 'serious problem', 'call doctor', 'hospital',
      'severe reaction', 'can\'t breathe', 'chest pain', 'heart attack',
      'overdose', 'poisoning', 'allergic reaction', 'call 911'
    ],
    keywords: [
      'emergency', 'urgent', 'serious', 'severe', 'hospital', 'doctor',
      'breathe', 'chest', 'heart', 'attack', 'overdose', 'poison', 'allergic', '911'
    ],
    entities: ['symptom', 'severity']
  }
};
