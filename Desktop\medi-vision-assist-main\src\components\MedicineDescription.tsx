
import React, { useState } from 'react';
import { <PERSON>Text, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/integrations/supabase/client';

interface MedicineDescriptionProps {
  onResult: (result: any) => void;
}

const MedicineDescription: React.FC<MedicineDescriptionProps> = ({ onResult }) => {
  const { t } = useTranslation();
  const [description, setDescription] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const extractMedicineFromDescription = (text: string): string => {
    // Simple extraction logic - look for potential medicine names
    const words = text.split(/\s+/);
    const commonMedicines = [
      'paracetamol', 'acetaminophen', 'ibuprofen', 'aspirin', 'amoxicillin',
      'lisinopril', 'metformin', 'amlodipine', 'omeprazole', 'simvastatin',
      'tylenol', 'advil', 'motrin', 'aleve', 'benadryl'
    ];
    
    // Look for medicine names in the description
    for (const word of words) {
      const cleanWord = word.toLowerCase().replace(/[^a-z]/g, '');
      if (commonMedicines.includes(cleanWord)) {
        return word.replace(/[^a-zA-Z]/g, '');
      }
    }
    
    // If no known medicine found, return the first capitalized word that might be a medicine name
    for (const word of words) {
      if (word.length > 4 && /^[A-Z]/.test(word)) {
        return word.replace(/[^a-zA-Z]/g, '');
      }
    }
    
    return words[0] || 'Unknown';
  };

  const lookupMedicine = async (medicineName: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('medicine-lookup', {
        body: { medicineName }
      });

      if (error) {
        console.error('Medicine lookup error:', error);
        throw error;
      }

      if (data.success && data.data) {
        return {
          confidence: data.data.medicine.confidence || 85,
          medicine: data.data.medicine,
          rxcui: data.data.rxcui
        };
      } else {
        return {
          confidence: 0,
          medicine: {
            product: medicineName,
            type: 'Unknown',
            usage: data.message || 'Could not identify medicine from description',
            dosage: 'Consult healthcare provider',
            similar: data.suggestions ? data.suggestions.join(', ') : 'Try more specific description'
          },
          error: true
        };
      }
    } catch (error) {
      console.error('Error looking up medicine:', error);
      return {
        confidence: 0,
        medicine: {
          product: 'Processing Error',
          type: 'Unknown',
          usage: 'Unable to process description',
          dosage: 'Consult healthcare provider',
          similar: 'None available'
        },
        error: true
      };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) return;

    setIsProcessing(true);
    try {
      // Extract potential medicine name from description
      const extractedName = extractMedicineFromDescription(description);
      
      // Lookup the medicine
      const result = await lookupMedicine(extractedName);
      onResult(result);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{t('identify.describeMedicine')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Textarea
                placeholder="Describe the medicine: color, shape, size, any text or numbers visible, packaging details, etc.&#10;&#10;Example: 'White round tablet with 500 printed on one side and a line on the other side. Comes in a blister pack labeled for pain relief.'"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="min-h-32 w-full"
                disabled={isProcessing}
              />
            </div>
            <Button 
              type="submit" 
              disabled={!description.trim() || isProcessing}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing Description...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Identify from Description
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Alert>
        <AlertDescription>
          <strong>Description Tips:</strong>
          <ul className="mt-2 space-y-1">
            <li>• Include physical characteristics: color, shape, size</li>
            <li>• Mention any text, numbers, or symbols on the medicine</li>
            <li>• Describe the packaging (bottle, blister pack, etc.)</li>
            <li>• Include brand names or any visible manufacturer information</li>
            <li>• The more details you provide, the better the identification</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default MedicineDescription;
