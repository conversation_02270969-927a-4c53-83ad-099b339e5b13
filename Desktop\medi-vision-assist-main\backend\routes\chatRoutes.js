
const express = require('express');
const chatController = require('../controllers/chatController');
const { validateMessage } = require('../middleware/validation');

const router = express.Router();

// Chat endpoints
router.post('/message', validateMessage, chatController.processMessage);
router.get('/conversation/:sessionId', chatController.getConversation);
router.delete('/conversation/:sessionId', chatController.clearConversation);

module.exports = router;
