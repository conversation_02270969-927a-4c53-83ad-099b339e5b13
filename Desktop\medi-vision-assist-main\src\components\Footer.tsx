
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Shield, Mail, Phone, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-primary text-primary-foreground py-12 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-2xl font-bold mb-4">MediScan</h3>
            <p className="text-primary-foreground/80 mb-4">
              AI-powered medicine identification for everyone. Safe, secure, and accessible.
            </p>
            <div className="flex items-center space-x-2 text-sm">
              <Shield className="h-4 w-4" />
              <span>Healthcare Grade Security</span>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Features</h4>
            <ul className="space-y-2 text-primary-foreground/80">
              <li><Link to="/identify" className="hover:text-primary-foreground transition-colors">Medicine Identification</Link></li>
              <li><Link to="/chat" className="hover:text-primary-foreground transition-colors">AI Assistant</Link></li>
              <li><Link to="/dashboard" className="hover:text-primary-foreground transition-colors">Personal Dashboard</Link></li>
              <li><span>Multilingual Support</span></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Support</h4>
            <ul className="space-y-2 text-primary-foreground/80">
              <li><Link to="/help" className="hover:text-primary-foreground transition-colors">Help Center</Link></li>
              <li><Link to="/accessibility" className="hover:text-primary-foreground transition-colors">Accessibility</Link></li>
              <li><Link to="/privacy" className="hover:text-primary-foreground transition-colors">Privacy Policy</Link></li>
              <li><Link to="/terms" className="hover:text-primary-foreground transition-colors">Terms of Service</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-3 text-primary-foreground/80">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Healthcare District, Medical Plaza</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-primary-foreground/60">
          <p>&copy; 2025 MediScan by Med Amine Chouchane. All rights reserved. Built for healthcare accessibility.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
