
import React, { useState } from 'react';
import { Search, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/integrations/supabase/client';

interface MedicineSearchProps {
  onResult: (result: any) => void;
}

const MedicineSearch: React.FC<MedicineSearchProps> = ({ onResult }) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const searchMedicine = async (medicineName: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('medicine-lookup', {
        body: { medicineName }
      });

      if (error) {
        console.error('Medicine search error:', error);
        throw error;
      }

      if (data.success && data.data) {
        return {
          confidence: data.data.medicine.confidence || 95,
          medicine: data.data.medicine,
          rxcui: data.data.rxcui
        };
      } else {
        return {
          confidence: 0,
          medicine: {
            product: searchTerm,
            type: 'Unknown',
            usage: data.message || 'Medicine not found in database',
            dosage: 'Consult healthcare provider',
            similar: data.suggestions ? data.suggestions.join(', ') : 'None available'
          },
          error: true,
          suggestions: data.suggestions
        };
      }
    } catch (error) {
      console.error('Error searching medicine:', error);
      return {
        confidence: 0,
        medicine: {
          product: searchTerm,
          type: 'Unknown',
          usage: 'Unable to retrieve medicine information',
          dosage: 'Consult healthcare provider',
          similar: 'None available'
        },
        error: true
      };
    }
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim()) return;

    setIsSearching(true);
    try {
      const result = await searchMedicine(searchTerm.trim());
      onResult(result);
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>{t('identify.searchByName')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div>
              <Input
                type="text"
                placeholder="Enter medicine name (e.g., Paracetamol, Ibuprofen, Aspirin)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
                disabled={isSearching}
              />
            </div>
            <Button 
              type="submit" 
              disabled={!searchTerm.trim() || isSearching}
              className="w-full"
            >
              {isSearching ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Search Medicine
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Alert>
        <AlertDescription>
          <strong>Search Tips:</strong>
          <ul className="mt-2 space-y-1">
            <li>• Try both brand names (e.g., "Tylenol") and generic names (e.g., "Acetaminophen")</li>
            <li>• Check spelling - the system will suggest corrections if needed</li>
            <li>• Include strength if known (e.g., "Ibuprofen 200mg")</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default MedicineSearch;
