
import React, { useState } from 'react';
import { <PERSON>, EyeOff, Lock, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useTranslation } from '@/hooks/useTranslation';

interface SecurityData {
  password: string;
  confirmPassword: string;
}

interface SecuritySectionProps {
  formData: SecurityData;
  onFormDataChange: (data: Partial<SecurityData>) => void;
  onPasswordChange: (password: string) => void;
  passwordStrength: number;
}

const SecuritySection = ({ formData, onFormDataChange, onPasswordChange, passwordStrength }: SecuritySectionProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { t } = useTranslation();

  const getPasswordStrengthText = (strength: number) => {
    switch (strength) {
      case 0:
      case 1: return t('register.weak');
      case 2:
      case 3: return t('register.medium');
      case 4:
      case 5: return t('register.strong');
      default: return t('register.weak');
    }
  };

  const getPasswordStrengthColor = (strength: number) => {
    switch (strength) {
      case 0:
      case 1: return "bg-red-500";
      case 2:
      case 3: return "bg-yellow-500";
      case 4:
      case 5: return "bg-green-500";
      default: return "bg-gray-300";
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center">
        <Shield className="h-5 w-5 mr-2" />
        {t('register.securityInfo')}
      </h3>

      <div className="space-y-2">
        <Label htmlFor="password">{t('register.password')}</Label>
        <div className="relative">
          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            placeholder={t('register.passwordPlaceholder')}
            value={formData.password}
            onChange={(e) => onPasswordChange(e.target.value)}
            className="pl-10 pr-10"
            required
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
        {formData.password && (
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all ${getPasswordStrengthColor(passwordStrength)}`}
                  style={{ width: `${(passwordStrength / 5) * 100}%` }}
                />
              </div>
              <span className="text-xs font-medium">
                {getPasswordStrengthText(passwordStrength)}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              {t('register.passwordRequirement')}
            </p>
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword">{t('register.confirmPassword')}</Label>
        <div className="relative">
          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? "text" : "password"}
            placeholder={t('register.confirmPasswordPlaceholder')}
            value={formData.confirmPassword}
            onChange={(e) => onFormDataChange({ confirmPassword: e.target.value })}
            className="pl-10 pr-10"
            required
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
        {formData.confirmPassword && formData.password !== formData.confirmPassword && (
          <p className="text-xs text-red-600">{t('register.passwordMismatch')}</p>
        )}
      </div>
    </div>
  );
};

export default SecuritySection;
