
import React from 'react';
import { User, Mail, Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from '@/hooks/useTranslation';

interface PersonalInfoData {
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth: string;
  language: string;
}

interface PersonalInfoSectionProps {
  formData: PersonalInfoData;
  onFormDataChange: (data: Partial<PersonalInfoData>) => void;
}

const PersonalInfoSection = ({ formData, onFormDataChange }: PersonalInfoSectionProps) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center">
        <User className="h-5 w-5 mr-2" />
        {t('register.personalInfo')}
      </h3>
      
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">{t('register.firstName')}</Label>
          <Input
            id="firstName"
            placeholder={t('register.firstNamePlaceholder')}
            value={formData.firstName}
            onChange={(e) => onFormDataChange({ firstName: e.target.value })}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">{t('register.lastName')}</Label>
          <Input
            id="lastName"
            placeholder={t('register.lastNamePlaceholder')}
            value={formData.lastName}
            onChange={(e) => onFormDataChange({ lastName: e.target.value })}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">{t('register.email')}</Label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="email"
            type="email"
            placeholder={t('register.emailPlaceholder')}
            value={formData.email}
            onChange={(e) => onFormDataChange({ email: e.target.value })}
            className="pl-10"
            required
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="dateOfBirth">{t('register.dateOfBirth')}</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="dateOfBirth"
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => onFormDataChange({ dateOfBirth: e.target.value })}
              className="pl-10"
              required
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="language">{t('register.preferredLanguage')}</Label>
          <Select 
            value={formData.language} 
            onValueChange={(value) => onFormDataChange({ language: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">{t('common.english')}</SelectItem>
              <SelectItem value="fr">{t('common.french')}</SelectItem>
              <SelectItem value="de">{t('common.german')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoSection;
