import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Camera, Search, Type } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import Navigation from '@/components/Navigation';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/integrations/supabase/client';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  intent?: string;
  confidence?: number;
}

interface ApiResponse {
  success: boolean;
  data?: {
    response: string;
    intent: string;
    confidence: number;
    suggestions: string[];
    sessionId: string;
  };
  error?: string;
  message?: string;
}

const ChatAssistant = () => {
  const { t } = useTranslation();
  
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: "Hello! I'm your AI medical assistant, powered by MediScan, a product by Med <PERSON>. I'm here to help you identify medicines, understand symptoms, and provide accurate health guidance. How can I assist you today?",
      timestamp: new Date(),
      suggestions: [
        "Identify a medicine by photo",
        "Search for a medicine by name", 
        "Ask about symptoms",
        "Learn about this app"
      ]
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessageToAPI = async (message: string): Promise<ApiResponse> => {
    try {
      const { data, error } = await supabase.functions.invoke('medical-chat', {
        body: {
          message,
          sessionId
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      return {
        success: false,
        error: 'Failed to connect to chatbot service',
        message: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment."
      };
    }
  };

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Send to API
    const apiResponse = await sendMessageToAPI(content);
    
    let botResponse: Message;
    
    if (apiResponse.success && apiResponse.data) {
      botResponse = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: apiResponse.data.response,
        timestamp: new Date(),
        suggestions: apiResponse.data.suggestions,
        intent: apiResponse.data.intent,
        confidence: apiResponse.data.confidence
      };
    } else {
      // Enhanced fallback response with language support information
      botResponse = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: apiResponse.message || "I want to help you, but I'm not sure I understood correctly. Could you please rephrase your question? I'm great at identifying medicines, providing health guidance, and explaining our app features. Please note that I currently support English only, but Arabic and German support will be available soon.",
        timestamp: new Date(),
        suggestions: [
          "Upload a photo to identify medicine",
          "Search by medicine name", 
          "Describe your symptoms",
          "Tell me about this app"
        ]
      };
    }

    setMessages(prev => [...prev, botResponse]);
    setIsTyping(false);
  };

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputMessage);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="h-[600px] flex flex-col">
          <CardHeader className="bg-primary text-primary-foreground">
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-6 w-6" />
              <span>AI Medical Assistant</span>
            </CardTitle>
          </CardHeader>
          
          <CardContent className="flex-1 flex flex-col p-0">
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`flex space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.type === 'user' ? 'bg-green-600' : 'bg-primary'
                      }`}>
                        {message.type === 'user' ? (
                          <User className="h-4 w-4 text-white" />
                        ) : (
                          <Bot className="h-4 w-4 text-white" />
                        )}
                      </div>
                      <div className={`rounded-lg p-3 ${
                        message.type === 'user' 
                          ? 'bg-green-600 text-white' 
                          : 'bg-muted'
                      }`}>
                        <p className="whitespace-pre-line">{message.content}</p>
                        {message.intent && message.confidence && (
                          <p className="text-xs opacity-50 mt-1">
                            Intent: {message.intent} ({Math.round(message.confidence * 100)}%)
                          </p>
                        )}
                        <p className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="flex space-x-2">
                      <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                      <div className="bg-muted rounded-lg p-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Suggestions */}
                {messages.length > 0 && messages[messages.length - 1].suggestions && !isTyping && (
                  <div className="flex flex-wrap gap-2 ml-10">
                    {messages[messages.length - 1].suggestions!.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="text-xs"
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            
            <div className="border-t p-4">
              <div className="flex space-x-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me about medicines, symptoms, or how this app works..."
                  className="flex-1"
                  disabled={isTyping}
                />
                <Button
                  onClick={() => sendMessage(inputMessage)}
                  disabled={!inputMessage.trim() || isTyping}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex space-x-2 mt-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="text-xs"
                  onClick={() => sendMessage("I want to identify a medicine by photo")}
                >
                  <Camera className="h-3 w-3 mr-1" />
                  Photo ID
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="text-xs"
                  onClick={() => sendMessage("Search for a medicine by name")}
                >
                  <Search className="h-3 w-3 mr-1" />
                  Name Search
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="text-xs"
                  onClick={() => sendMessage("I want to describe my symptoms")}
                >
                  <Type className="h-3 w-3 mr-1" />
                  Symptoms
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ChatAssistant;
