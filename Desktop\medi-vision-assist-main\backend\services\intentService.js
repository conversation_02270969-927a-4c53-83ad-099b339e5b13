
const intents = require('../config/intents');

class IntentService {
  detectIntent(message) {
    const normalizedMessage = message.toLowerCase().trim();
    let bestMatch = {
      name: 'unknown',
      confidence: 0,
      entities: {}
    };

    // Check each intent for pattern matches
    for (const [intentName, intentConfig] of Object.entries(intents)) {
      const confidence = this.calculateConfidence(normalizedMessage, intentConfig);
      
      if (confidence > bestMatch.confidence) {
        bestMatch = {
          name: intentName,
          confidence,
          entities: this.extractEntities(normalizedMessage, intentConfig.entities || [])
        };
      }
    }

    // Set minimum confidence threshold
    if (bestMatch.confidence < 0.4) {
      // Check for emergency keywords with lower threshold
      if (this.isEmergency(normalizedMessage)) {
        return {
          name: 'emergency',
          confidence: 1.0,
          entities: {}
        };
      }
      
      bestMatch.name = 'unknown';
      bestMatch.confidence = 0;
    }

    console.log(`Intent detected: ${bestMatch.name} (confidence: ${bestMatch.confidence})`);
    return bestMatch;
  }

  calculateConfidence(message, intentConfig) {
    const { patterns, keywords } = intentConfig;
    let score = 0;
    let maxScore = 0;

    // Check exact pattern matches (higher weight)
    if (patterns) {
      maxScore += patterns.length * 2;
      patterns.forEach(pattern => {
        if (message.includes(pattern.toLowerCase())) {
          score += 2;
        }
      });
    }

    // Check keyword matches (lower weight)
    if (keywords) {
      maxScore += keywords.length;
      keywords.forEach(keyword => {
        if (message.includes(keyword.toLowerCase())) {
          score += 1;
        }
      });
    }

    // Bonus for multiple word matches
    const messageWords = message.split(' ');
    const allPatternWords = (patterns || []).join(' ').split(' ');
    const allKeywords = keywords || [];
    const allTerms = [...allPatternWords, ...allKeywords];
    
    let wordMatches = 0;
    messageWords.forEach(word => {
      if (allTerms.some(term => term.toLowerCase().includes(word) || word.includes(term.toLowerCase()))) {
        wordMatches++;
      }
    });
    
    if (wordMatches > 1) {
      score += wordMatches * 0.5;
      maxScore += wordMatches * 0.5;
    }

    return maxScore > 0 ? Math.min(score / maxScore, 1) : 0;
  }

  isEmergency(message) {
    const emergencyKeywords = [
      'emergency', 'urgent', 'serious', 'severe', 'hospital', 'doctor',
      'can\'t breathe', 'chest pain', 'heart attack', 'stroke', 'bleeding',
      'overdose', 'poisoning', 'allergic reaction', 'call 911'
    ];
    
    return emergencyKeywords.some(keyword => message.includes(keyword));
  }

  extractEntities(message, entityTypes) {
    const entities = {};
    
    entityTypes.forEach(entityType => {
      switch (entityType) {
        case 'symptom':
          entities.symptoms = this.extractSymptoms(message);
          break;
        case 'medicine':
          entities.medicines = this.extractMedicines(message);
          break;
        case 'body_part':
          entities.bodyParts = this.extractBodyParts(message);
          break;
        case 'severity':
          entities.severity = this.extractSeverity(message);
          break;
      }
    });

    return entities;
  }

  extractSymptoms(message) {
    const symptoms = [
      'headache', 'fever', 'cough', 'nausea', 'pain', 'ache', 'sore',
      'stomach', 'back', 'chest', 'throat', 'dizzy', 'tired', 'fatigue',
      'runny nose', 'congestion', 'sneezing', 'itchy', 'rash', 'swelling',
      'burning', 'tingling', 'cramping', 'bloating', 'diarrhea', 'constipation'
    ];
    
    return symptoms.filter(symptom => 
      message.includes(symptom.toLowerCase())
    );
  }

  extractMedicines(message) {
    const commonMedicines = [
      'aspirin', 'ibuprofen', 'acetaminophen', 'tylenol', 'advil',
      'paracetamol', 'antibiotic', 'vitamin', 'supplement', 'insulin',
      'blood pressure', 'cholesterol', 'antacid', 'allergy', 'cold medicine'
    ];
    
    return commonMedicines.filter(medicine => 
      message.includes(medicine.toLowerCase())
    );
  }

  extractBodyParts(message) {
    const bodyParts = [
      'head', 'stomach', 'back', 'chest', 'throat', 'leg', 'arm',
      'hand', 'foot', 'neck', 'shoulder', 'knee', 'ankle', 'eye',
      'ear', 'nose', 'mouth', 'teeth', 'skin', 'joints'
    ];
    
    return bodyParts.filter(part => 
      message.includes(part.toLowerCase())
    );
  }

  extractSeverity(message) {
    if (message.includes('severe') || message.includes('terrible') || message.includes('excruciating')) {
      return 'severe';
    }
    if (message.includes('mild') || message.includes('slight') || message.includes('little')) {
      return 'mild';
    }
    if (message.includes('moderate') || message.includes('medium')) {
      return 'moderate';
    }
    return 'unknown';
  }
}

module.exports = new IntentService();
