
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Enhanced intent detection configuration with 100+ new patterns
const intents = {
  greet: {
    patterns: [
      'hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening',
      'greetings', 'what\'s up', 'howdy', 'hi there', 'hello there'
    ]
  },
  ask_bot_status: {
    patterns: [
      'how are you', 'what\'s up', 'how do you do', 'how are things',
      'are you working', 'are you online', 'are you there', 'status check',
      'how can you help me', 'what can you do for me', 'how do you help',
      'how can you help', 'what can you do', 'help me'
    ]
  },
  creator_question: {
    patterns: [
      'who created you', 'who made you', 'who developed you', 'who built you',
      'who is your creator', 'who designed you', 'your creator', 'your developer'
    ]
  },
  capabilities_question: {
    patterns: [
      'what can you do', 'what are your capabilities', 'what do you do',
      'how can you help', 'what services do you provide', 'your functions'
    ]
  },
  doctor_question: {
    patterns: [
      'are you a real doctor', 'are you a doctor', 'are you medical professional',
      'do you have medical degree', 'qualified doctor'
    ]
  },
  information_source: {
    patterns: [
      'how do you get your information', 'where is your information from',
      'source of information', 'how do you know', 'your knowledge source'
    ]
  },
  diagnosis_question: {
    patterns: [
      'can you diagnose', 'can you diagnose diseases', 'do you diagnose',
      'medical diagnosis', 'can you tell what disease i have'
    ]
  },
  privacy_question: {
    patterns: [
      'do you store my data', 'is my data safe', 'privacy policy',
      'personal data', 'data protection', 'confidential'
    ]
  },
  trust_question: {
    patterns: [
      'can i trust your advice', 'is your advice reliable', 'can i trust you',
      'how accurate are you', 'reliable information'
    ]
  },
  availability_question: {
    patterns: [
      'are you available 24/7', 'always available', 'when are you available',
      'working hours', 'available anytime'
    ]
  },
  side_effects_question: {
    patterns: [
      'medication side effects', 'drug side effects', 'side effects',
      'adverse effects', 'medicine reactions'
    ]
  },
  emergency_question: {
    patterns: [
      'what to do in emergency', 'medical emergency', 'emergency situation',
      'urgent help', 'emergency help'
    ]
  },
  language_support: {
    patterns: [
      'other languages', 'different languages', 'language support',
      'arabic support', 'german support', 'multilingual'
    ]
  },
  flu_symptoms: {
    patterns: [
      'flu symptoms', 'influenza symptoms', 'common cold symptoms',
      'fever and cough', 'body aches'
    ]
  },
  medicine_identification: {
    patterns: [
      'identify this medicine', 'what is this pill', 'unknown medication', 'can you identify',
      'what medicine is this', 'identify pill', 'what drug is this', 'medicine identification',
      'pill identification', 'identify medication', 'what tablet is this',
      'medicine appearance', 'pill shape', 'medicine color'
    ]
  },
  drug_interactions: {
    patterns: [
      'drug interactions', 'medicine interactions', 'can i take together',
      'mixing medicines', 'combining drugs'
    ]
  },
  missed_dose: {
    patterns: [
      'missed dose', 'forgot to take medicine', 'skipped dose',
      'what if i miss dose', 'missed medication'
    ]
  },
  food_interactions: {
    patterns: [
      'food and medicine', 'food interactions', 'eating with medicine',
      'food affects medication', 'dietary restrictions'
    ]
  },
  paracetamol_dosage: {
    patterns: [
      'paracetamol dosage', 'acetaminophen dose', 'tylenol dosage',
      'how much paracetamol', 'paracetamol amount'
    ]
  },
  allergy_symptoms: {
    patterns: [
      'allergy symptoms', 'allergic reaction', 'allergy signs',
      'skin rash', 'itching', 'swelling'
    ]
  },
  generic_vs_brand: {
    patterns: [
      'generic vs brand', 'generic medicine', 'brand name drug',
      'difference generic brand', 'generic equivalent'
    ]
  },
  antibiotics_timing: {
    patterns: [
      'how long antibiotics work', 'antibiotic effectiveness',
      'when do antibiotics start working', 'antibiotic course'
    ]
  },
  heart_attack_symptoms: {
    patterns: [
      'heart attack symptoms', 'chest pain', 'heart attack signs',
      'cardiac symptoms', 'myocardial infarction'
    ]
  },
  diabetes_symptoms: {
    patterns: [
      'diabetes symptoms', 'diabetic signs', 'high blood sugar symptoms',
      'diabetes warning signs'
    ]
  },
  vaccine_info: {
    patterns: [
      'how vaccines work', 'vaccine effectiveness', 'vaccination',
      'immunization', 'vaccine safety'
    ]
  },
  herbal_safety: {
    patterns: [
      'herbal medicine safety', 'natural remedies', 'herbal supplements',
      'plant medicine', 'traditional medicine'
    ]
  },
  dehydration_symptoms: {
    patterns: [
      'dehydration symptoms', 'dehydrated signs', 'lack of water',
      'dry mouth', 'thirst'
    ]
  },
  breastfeeding_medication: {
    patterns: [
      'breastfeeding and medication', 'nursing and medicine',
      'safe while breastfeeding', 'lactation medication'
    ]
  },
  serious_rash: {
    patterns: [
      'serious rash', 'when is rash serious', 'skin condition',
      'rash warning signs', 'dangerous rash'
    ]
  },
  anemia_symptoms: {
    patterns: [
      'anemia symptoms', 'low iron symptoms', 'anemic signs',
      'iron deficiency', 'tired all the time'
    ]
  },
  blood_pressure_meds: {
    patterns: [
      'blood pressure medication', 'hypertension drugs',
      'bp medicine', 'high blood pressure pills'
    ]
  },
  stroke_symptoms: {
    patterns: [
      'stroke symptoms', 'stroke warning signs', 'brain attack',
      'stroke signs', 'sudden weakness'
    ]
  },
  fever_management: {
    patterns: [
      'manage fever', 'reduce fever', 'fever treatment',
      'high temperature', 'fever remedies'
    ]
  },
  empty_stomach_medicine: {
    patterns: [
      'medicine empty stomach', 'take with food', 'before or after eating',
      'food with medicine', 'stomach medication'
    ]
  },
  asthma_symptoms: {
    patterns: [
      'asthma symptoms', 'breathing problems', 'wheezing',
      'chest tightness', 'shortness of breath'
    ]
  },
  antibiotic_resistance: {
    patterns: [
      'antibiotic resistance', 'resistant bacteria', 'overuse antibiotics',
      'superbug', 'antibiotic effectiveness'
    ]
  },
  painkiller_addiction: {
    patterns: [
      'painkiller addiction', 'pain medicine dependence',
      'opioid addiction', 'addictive painkillers'
    ]
  },
  antidepressant_effects: {
    patterns: [
      'antidepressant side effects', 'depression medication effects',
      'ssri side effects', 'mood medicine'
    ]
  },
  allergic_reaction_explain: {
    patterns: [
      'what is allergic reaction', 'allergy explanation',
      'immune system reaction', 'allergic response'
    ]
  },
  viral_vs_bacterial: {
    patterns: [
      'viral vs bacterial infection', 'virus or bacteria',
      'difference viral bacterial', 'infection type'
    ]
  },
  blood_thinner_precautions: {
    patterns: [
      'blood thinner precautions', 'anticoagulant safety',
      'warfarin precautions', 'blood thinning medication'
    ]
  },
  medicine_storage: {
    patterns: [
      'store medicines', 'medicine storage', 'how to store pills',
      'medication storage', 'keep medicine safe'
    ]
  },
  covid_symptoms: {
    patterns: [
      'covid symptoms', 'coronavirus symptoms', 'covid-19 signs',
      'pandemic symptoms', 'sars-cov-2'
    ]
  },
  alcohol_medication: {
    patterns: [
      'alcohol with medication', 'drinking with medicine',
      'alcohol drug interaction', 'can i drink alcohol'
    ]
  },
  painkiller_purpose: {
    patterns: [
      'purpose of painkillers', 'why use painkillers',
      'pain relief medicine', 'analgesic purpose'
    ]
  },
  dispose_medicines: {
    patterns: [
      'dispose expired medicines', 'throw away pills',
      'medicine disposal', 'get rid of old medicine'
    ]
  },
  child_medicine_accident: {
    patterns: [
      'child swallowed medicine', 'accidental medicine ingestion',
      'child ate pills', 'medicine poisoning child'
    ]
  },
  app_info: {
    patterns: [
      'what is this app', 'what does this do', 'how does this work', 'what can you do',
      'tell me about this app', 'app purpose', 'what is this for', 'how can you help',
      'what are your features', 'what can this app do', 'explain this app',
      'for what this platform', 'what this platform', 'platform purpose',
      'about this app', 'app info', 'what is mediscan', 'tell me about'
    ]
  },
  medicine_search: {
    patterns: [
      'search by medicine name', 'search medicine', 'find medicine', 'medicine search',
      'look up medicine', 'find drug', 'search by name', 'search by generic name',
      'find by name', 'lookup medicine', 'medicine lookup'
    ]
  },
  symptom_query: {
    patterns: [
      'i have a headache', 'my stomach hurts', 'i feel sick', 'i am in pain',
      'something hurts', 'i don\'t feel well', 'i have symptoms', 'i\'m not feeling good',
      'my head hurts', 'stomach pain', 'back pain', 'chest pain', 'sore throat',
      'describe your symptoms', 'describe symptoms', 'symptoms', 'i have pain'
    ]
  }
};

// Enhanced response templates with 100+ new responses
const responses = {
  greet: {
    messages: [
      "Hello! I'm your AI medical assistant, powered by MediScan, a product by Med Amine Chouchane. I'm here to help you identify medicines, understand symptoms, and provide accurate health guidance. How can I assist you today?",
      "Hi there! Welcome to MediScan, created by Med Amine Chouchane. I'm your AI medical assistant ready to help you with medicine identification and health questions. What would you like to know?",
      "Hello! I'm your personal AI medical assistant from MediScan by Med Amine Chouchane. I can help you identify medicines, understand symptoms, and provide health guidance. How may I assist you?"
    ],
    suggestions: [
      "Identify a medicine by photo",
      "Search for a medicine by name",
      "Ask about symptoms",
      "Learn about this app"
    ]
  },
  ask_bot_status: {
    messages: [
      "I'm doing great and ready to help! I can assist you with medicine identification, symptom guidance, and health information. You can upload photos of medicines, search by name, or describe your symptoms.",
      "I'm working perfectly and here to help you! I specialize in identifying medicines through photos or descriptions, providing symptom guidance, and offering health information.",
      "I'm online and ready! I can help you identify unknown medicines, search our medicine database, understand symptoms, and provide health guidance."
    ],
    suggestions: [
      "Upload medicine photo",
      "Search medicine database", 
      "Ask about symptoms",
      "Learn about app features"
    ]
  },
  creator_question: {
    messages: [
      "I was developed by Med Amine Chouchane to assist users with medicine identification, symptom checking, and health advice. I'm designed to make healthcare information more accessible to everyone.",
      "Med Amine Chouchane created me as part of the MediScan platform to help people with medical questions and medicine identification. My goal is to provide reliable health guidance.",
      "I'm the creation of Med Amine Chouchane, built specifically to help users identify medicines, understand symptoms, and provide accurate health information through AI technology."
    ],
    suggestions: [
      "What can you do?",
      "How do you get your information?",
      "Are you a real doctor?",
      "Can I trust your advice?"
    ]
  },
  capabilities_question: {
    messages: [
      "I can help identify medicines, interpret symptoms, provide general health advice, and answer questions related to medical topics. I'm your comprehensive health information assistant.",
      "My capabilities include medicine identification through photos or descriptions, symptom analysis, health guidance, medication information, and general medical advice - all to support your healthcare decisions.",
      "I can assist with identifying unknown medicines, explaining symptoms, providing medication information, offering health guidance, and answering medical questions to help you make informed decisions."
    ],
    suggestions: [
      "Upload medicine photo",
      "Ask about symptoms",
      "Search medication info",
      "Get health advice"
    ]
  },
  doctor_question: {
    messages: [
      "No, I am an AI assistant designed to provide helpful medical information, but I do not replace professional medical advice or diagnosis. Always consult healthcare professionals for medical decisions.",
      "I'm not a real doctor - I'm an AI health assistant created to provide medical information and guidance. For diagnosis and treatment, you should always consult with qualified healthcare professionals.",
      "I'm an AI medical assistant, not a licensed physician. While I provide reliable health information, I cannot replace the expertise and judgment of real healthcare professionals."
    ],
    suggestions: [
      "Find healthcare provider",
      "Emergency contact info",
      "When to see a doctor",
      "General health advice"
    ]
  },
  information_source: {
    messages: [
      "My knowledge is based on verified medical sources and data curated by health experts to provide accurate information. I use trusted medical databases and research.",
      "I get my information from reliable medical databases, peer-reviewed research, and trusted healthcare sources that are regularly updated by medical professionals.",
      "My medical knowledge comes from verified healthcare databases, medical literature, and expert-curated content to ensure I provide accurate and up-to-date information."
    ],
    suggestions: [
      "Ask specific medical question",
      "Learn about data accuracy",
      "Get medication info",
      "Symptom guidance"
    ]
  },
  diagnosis_question: {
    messages: [
      "I can provide guidance based on symptoms but cannot perform a medical diagnosis. Please consult a healthcare professional for proper diagnosis and treatment plans.",
      "While I can help interpret symptoms and provide health information, I cannot diagnose medical conditions. Only qualified healthcare professionals can provide accurate diagnoses.",
      "I offer symptom guidance and health information, but medical diagnosis requires a healthcare professional's expertise. I recommend consulting your doctor for proper diagnosis."
    ],
    suggestions: [
      "Describe your symptoms",
      "Find healthcare provider",
      "When to seek medical help",
      "General health guidance"
    ]
  },
  privacy_question: {
    messages: [
      "Your privacy is important. I do not store personal data unless explicitly authorized and always follow strict privacy policies to protect your information.",
      "Data privacy is enforced using industry-standard security protocols to protect your information. We follow strict confidentiality guidelines for all interactions.",
      "All conversations are treated with strict confidentiality. Your personal health information is protected using secure, encrypted systems and privacy-focused policies."
    ],
    suggestions: [
      "Learn about data protection",
      "Privacy policy details",
      "Confidentiality information",
      "Security measures"
    ]
  },
  trust_question: {
    messages: [
      "I provide information based on reliable medical sources, but my advice should not replace consultation with a healthcare provider. Always verify important medical decisions with professionals.",
      "My information is sourced from trusted medical databases and regularly updated, but always verify with a healthcare professional for important medical decisions.",
      "While I use verified medical sources and expert-curated data, I recommend confirming important health information with qualified healthcare professionals."
    ],
    suggestions: [
      "Verify with healthcare provider",
      "Get second opinion",
      "Learn about sources",
      "Medical consultation"
    ]
  },
  availability_question: {
    messages: [
      "Yes, I am available anytime to assist you with your medical questions. I'm here 24/7 to provide health information and guidance whenever you need it.",
      "I'm available 24/7 to help with medical questions, medicine identification, and health guidance. You can access my assistance anytime, day or night.",
      "Absolutely! I'm here around the clock to provide medical information and assistance. No matter what time zone or schedule, I'm ready to help."
    ],
    suggestions: [
      "Ask medical question now",
      "Identify medicine",
      "Symptom guidance",
      "Health information"
    ]
  },
  side_effects_question: {
    messages: [
      "Yes, I can provide information about common side effects of many medications. Please tell me which specific medicine you're asking about for detailed information.",
      "I can help explain medication side effects, drug interactions, and safety information. What specific medicine would you like to know about?",
      "I have comprehensive information about medication side effects and can help you understand what to expect. Which medicine are you concerned about?"
    ],
    suggestions: [
      "Search specific medicine",
      "Common side effects",
      "Drug interactions",
      "When to seek help"
    ]
  },
  emergency_question: {
    messages: [
      "In a medical emergency, please contact emergency services or go to the nearest hospital immediately. Don't delay - call 911 (US) or your local emergency number right away.",
      "For medical emergencies, call emergency services immediately (911 in the US) or go to your nearest emergency room. Time is critical in emergencies.",
      "⚠️ Medical emergencies require immediate professional help. Call emergency services (911) or go to the nearest hospital. Don't wait - get help now!"
    ],
    suggestions: [
      "Call Emergency Services (911)",
      "Find nearest hospital",
      "Emergency symptoms guide",
      "When to call 911"
    ]
  },
  language_support: {
    messages: [
      "Currently, I support English. Arabic and German support will be available soon. We're working to make healthcare information accessible in multiple languages.",
      "I currently operate in English only, but Arabic and German language support is coming soon. Stay tuned for multilingual capabilities!",
      "Right now I communicate in English, but we're actively developing Arabic and German support to serve more users in their preferred language."
    ],
    suggestions: [
      "Ask question in English",
      "Learn about upcoming features",
      "Get current help",
      "Language update info"
    ]
  },
  flu_symptoms: {
    messages: [
      "Common flu symptoms include fever, cough, sore throat, body aches, fatigue, and headache. Symptoms usually develop suddenly and can be quite severe.",
      "Influenza typically causes fever, muscle aches, cough, sore throat, runny nose, headache, and extreme fatigue. These symptoms often come on quickly.",
      "Flu symptoms include high fever, body aches, severe fatigue, dry cough, sore throat, and headache. Unlike a cold, flu symptoms are usually more severe and sudden."
    ],
    suggestions: [
      "Flu treatment options",
      "When to see doctor",
      "Prevent flu spread",
      "Flu vs cold differences"
    ]
  },
  medicine_identification: {
    messages: [
      "I'd be happy to help identify your medicine! For the most accurate results, please upload a clear photo of the medication showing any text, numbers, or markings.",
      "To identify a medicine by its appearance, describe the medicine's shape, color, size, and any imprints or markings. I can help identify it based on those details.",
      "I can help identify medicines from their physical characteristics. Please describe the pill's color, shape, size, and any text or numbers visible on it."
    ],
    suggestions: [
      "Upload medicine photo",
      "Describe pill appearance", 
      "Enter any visible text",
      "Check medicine markings"
    ]
  },
  drug_interactions: {
    messages: [
      "Drug interactions occur when one medicine affects the activity of another, potentially causing harmful effects. Please tell me which specific medicines you're asking about.",
      "Medicine interactions can be dangerous when drugs affect each other's effectiveness or increase side effects. What specific medications are you concerned about combining?",
      "I can help explain potential interactions between medications. Please specify which medicines you want to check for interactions."
    ],
    suggestions: [
      "Check specific medicines",
      "Common interactions",
      "Safety guidelines",
      "Consult pharmacist"
    ]
  },
  missed_dose: {
    messages: [
      "Generally, take the missed dose as soon as you remember unless it's near the next dose time. Never double up doses. Consult your doctor for specific guidance.",
      "If you miss a dose, take it when you remember unless it's almost time for the next dose. Don't take extra medicine to make up for missed doses.",
      "For missed doses, the general rule is to take it as soon as possible, but skip it if it's close to your next scheduled dose. Check with your healthcare provider for medicine-specific advice."
    ],
    suggestions: [
      "Specific medicine guidance",
      "Dosing schedule help",
      "Set medication reminders",
      "Consult healthcare provider"
    ]
  },
  food_interactions: {
    messages: [
      "Yes, some foods can interfere with medication effectiveness or increase side effects. Please tell me which specific medicines you're taking for detailed guidance.",
      "Certain foods can affect how your body absorbs or processes medications. Food-drug interactions vary by medicine - which medication are you asking about?",
      "Food can impact medication absorption and effectiveness. Some medicines should be taken with food, others on an empty stomach. What specific medicine concerns you?"
    ],
    suggestions: [
      "Check specific medicine",
      "Foods to avoid",
      "Timing with meals",
      "Dietary guidelines"
    ]
  },
  paracetamol_dosage: {
    messages: [
      "Typically, adults take 500-1000 mg of paracetamol every 4-6 hours, not exceeding 4000 mg in 24 hours. Always follow your doctor's instructions or package directions.",
      "The standard adult paracetamol dose is 500-1000mg every 4-6 hours, with a maximum of 4000mg per day. Children's doses are based on weight - consult packaging or healthcare provider.",
      "For adults, paracetamol dosage is usually 500-1000mg every 4-6 hours, maximum 4g daily. Never exceed the recommended dose and always check with healthcare providers for personal guidance."
    ],
    suggestions: [
      "Check package instructions",
      "Children's dosing",
      "Safety guidelines",
      "Overdose prevention"
    ]
  },
  allergy_symptoms: {
    messages: [
      "Common allergy symptoms include rash, itching, swelling, runny nose, sneezing, and watery eyes. Seek emergency help immediately if you experience difficulty breathing or severe swelling.",
      "Allergy symptoms can include skin rash, hives, itching, nasal congestion, sneezing, and digestive issues. Severe reactions may cause breathing problems - seek immediate medical help.",
      "Allergic reactions can cause itching, rash, swelling, congestion, and stomach upset. Watch for severe symptoms like difficulty breathing, which requires emergency medical attention."
    ],
    suggestions: [
      "Identify allergens",
      "Emergency symptoms",
      "Allergy management",
      "When to call 911"
    ]
  }
};

function detectIntent(message: string) {
  const normalizedMessage = message.toLowerCase().trim();
  console.log(`Detecting intent for: "${normalizedMessage}"`);
  
  let bestMatch = {
    name: 'unknown',
    confidence: 0
  };

  // Check each intent for pattern matches
  for (const [intentName, intentConfig] of Object.entries(intents)) {
    let matchCount = 0;
    
    // Check for pattern matches using substring matching and word boundaries
    for (const pattern of intentConfig.patterns) {
      const patternLower = pattern.toLowerCase();
      
      // Check for exact substring match
      if (normalizedMessage.includes(patternLower)) {
        matchCount += 3; // Higher weight for exact matches
        console.log(`Exact pattern match found: "${pattern}" in intent: ${intentName}`);
      }
      // Check for partial word matches
      else {
        const messageWords = normalizedMessage.split(/\s+/);
        const patternWords = patternLower.split(/\s+/);
        
        let wordMatches = 0;
        for (const patternWord of patternWords) {
          if (messageWords.some(messageWord => 
            messageWord.includes(patternWord) || patternWord.includes(messageWord)
          )) {
            wordMatches++;
          }
        }
        
        // If most words match, give it some score
        if (wordMatches >= Math.ceil(patternWords.length * 0.6)) {
          matchCount += Math.ceil(wordMatches * 1.5);
          console.log(`Partial pattern match found: "${pattern}" in intent: ${intentName} (${wordMatches}/${patternWords.length} words)`);
        }
      }
    }
    
    // Calculate confidence based on matches
    const confidence = matchCount > 0 ? Math.min(matchCount / (intentConfig.patterns.length * 2), 1) : 0;
    
    console.log(`Intent: ${intentName}, Matches: ${matchCount}, Confidence: ${confidence.toFixed(3)}`);
    
    if (confidence > bestMatch.confidence) {
      bestMatch = {
        name: intentName,
        confidence
      };
    }
  }

  // Lower confidence threshold for better matching
  if (bestMatch.confidence < 0.03) {
    bestMatch.name = 'unknown';
    bestMatch.confidence = 0;
  }

  console.log(`Best match: ${bestMatch.name} with confidence: ${bestMatch.confidence.toFixed(3)}`);
  return bestMatch;
}

function generateResponse(intent: any, originalMessage: string) {
  const intentName = intent.name;
  console.log(`Generating response for intent: ${intentName}`);
  
  if (responses[intentName as keyof typeof responses]) {
    const templateResponse = responses[intentName as keyof typeof responses];
    const randomMessage = templateResponse.messages[Math.floor(Math.random() * templateResponse.messages.length)];
    
    return {
      text: randomMessage,
      suggestions: templateResponse.suggestions || []
    };
  }

  // Enhanced fallback response with language support information
  return {
    text: "I want to help you, but I'm not sure I understood correctly. Could you please rephrase your question? I'm great at identifying medicines, providing health guidance, and explaining our app features. Please note that I currently support English only, but Arabic and German support will be available soon.",
    suggestions: [
      "Upload a photo to identify medicine",
      "Search by medicine name",
      "Describe your symptoms",
      "Tell me about this app"
    ]
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { message, sessionId = 'default' } = await req.json();
    
    console.log(`Processing message: "${message}" for session: ${sessionId}`);
    
    // Detect intent from user message
    const intent = detectIntent(message);
    
    // Generate appropriate response
    const response = generateResponse(intent, message);
    
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          response: response.text,
          intent: intent.name,
          confidence: intent.confidence,
          suggestions: response.suggestions,
          sessionId
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    );
  } catch (error) {
    console.error('Error processing message:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to process message',
        message: 'I apologize, but I encountered an error. Please try again.'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    );
  }
});
