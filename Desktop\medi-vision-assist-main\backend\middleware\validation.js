
const validateMessage = (req, res, next) => {
  const { message } = req.body;

  if (!message || typeof message !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: 'Message is required and must be a string'
    });
  }

  if (message.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: 'Message cannot be empty'
    });
  }

  if (message.length > 1000) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: 'Message must be less than 1000 characters'
    });
  }

  next();
};

module.exports = {
  validateMessage
};
