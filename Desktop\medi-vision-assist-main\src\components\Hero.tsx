
import React from 'react';
import { Camera, Search, Type } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-primary/5 to-background">
      <div className="container mx-auto max-w-6xl text-center">
        <h1 className="text-4xl md:text-6xl font-bold mb-6 text-foreground">
          Identify Medicine with <span className="text-primary">AI-Powered</span> Precision
        </h1>
        <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
          Upload a photo, enter a description, or chat with our AI assistant to instantly identify medications, 
          get dosage information, and learn about side effects. Accessible in English, French, and German.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
          <Link to="/identify?method=photo">
            <Button className="w-full h-32 bg-green-600 hover:bg-green-700 text-white flex flex-col items-center justify-center space-y-2">
              <Camera className="h-8 w-8" />
              <span className="text-lg font-semibold">Upload Photo</span>
              <span className="text-sm opacity-90">Take or upload image</span>
            </Button>
          </Link>
          
          <Link to="/identify?method=search">
            <Button className="w-full h-32 bg-green-600 hover:bg-green-700 text-white flex flex-col items-center justify-center space-y-2">
              <Search className="h-8 w-8" />
              <span className="text-lg font-semibold">Search by Name</span>
              <span className="text-sm opacity-90">Enter medicine name</span>
            </Button>
          </Link>
          
          <Link to="/identify?method=description">
            <Button className="w-full h-32 bg-green-600 hover:bg-green-700 text-white flex flex-col items-center justify-center space-y-2">
              <Type className="h-8 w-8" />
              <span className="text-lg font-semibold">Describe Medicine</span>
              <span className="text-sm opacity-90">Shape, color, text</span>
            </Button>
          </Link>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/chat">
            <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white">
              Chat with AI Assistant
            </Button>
          </Link>
          <Link to="/register">
            <Button variant="outline" size="lg">
              Create Free Account
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Hero;
